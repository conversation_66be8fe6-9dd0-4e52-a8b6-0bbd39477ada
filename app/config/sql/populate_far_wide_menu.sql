-- Create and populate Far & Wide navigation menu items
-- Run this SQL directly in your database

-- Create navigation_menus table if it doesn't exist
CREATE TABLE IF NOT EXISTS `navigation_menus` (
  `id` int(10) unsigned NOT NULL auto_increment,
  `parent_id` int(10) unsigned default NULL,
  `lft` int(10) unsigned default NULL,
  `rght` int(10) unsigned default NULL,
  `child_count` int(10) unsigned default NULL,
  `direct_child_count` int(10) unsigned default NULL,
  `name` varchar(255) NOT NULL default '',
  `url` varchar(500) NOT NULL default '',
  `menu_type` varchar(50) NOT NULL default 'main_nav',
  `order` int(10) unsigned default NULL,
  `published` tinyint(1) unsigned NOT NULL default '1',
  `created` datetime default NULL,
  `modified` datetime default NULL,
  PRIMARY KEY  (`id`),
  KEY `parent_id` (`parent_id`),
  KEY `menu_type` (`menu_type`),
  <PERSON>EY `published` (`published`),
  KEY `lft` (`lft`),
  KEY `rght` (`rght`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;

-- Clear existing Far & Wide menu items
DELETE FROM `navigation_menus` WHERE `menu_type` = 'far_wide';

-- Insert Far & Wide menu with hierarchical structure
-- All URLs use /landing_pages/ prefix, Cruises and Escorted Tours are children of Worldwide
INSERT INTO `navigation_menus` (`id`, `parent_id`, `lft`, `rght`, `child_count`, `direct_child_count`, `name`, `url`, `menu_type`, `order`, `published`, `created`, `modified`) VALUES
(1, NULL, 1, 10, 4, 2, 'Far & Wide', '#', 'far_wide', 1, 1, NOW(), NOW()),
(2, 1, 2, 3, 0, 0, 'South America', '/landing_pages/south_america', 'far_wide', 1, 1, NOW(), NOW()),
(3, 1, 4, 9, 2, 2, 'Worldwide', '/landing_pages/worldwide', 'far_wide', 2, 1, NOW(), NOW()),
(4, 3, 5, 6, 0, 0, 'Cruises', '/landing_pages/cruises', 'far_wide', 1, 1, NOW(), NOW()),
(5, 3, 7, 8, 0, 0, 'Escorted Tours', '/landing_pages/escorted_tours', 'far_wide', 2, 1, NOW(), NOW());

-- Verify the data was inserted
SELECT
    id,
    parent_id,
    lft,
    rght,
    name,
    url,
    menu_type,
    published
FROM navigation_menus
WHERE menu_type = 'far_wide'
ORDER BY lft;
