<?php
/* SVN FILE: $Id: routes.php 7945 2008-12-19 02:16:01Z gwoo $ */
/**
 * Short description for file.
 *
 * In this file, you set up routes to your controllers and their actions.
 * Routes are very important mechanism that allows you to freely connect
 * different urls to chosen controllers and their actions (functions).
 *
 * PHP versions 4 and 5
 *
 * CakePHP(tm) :  Rapid Development Framework (http://www.cakephp.org)
 * Copyright 2005-2008, Cake Software Foundation, Inc. (http://www.cakefoundation.org)
 *
 * Licensed under The MIT License
 * Redistributions of files must retain the above copyright notice.
 *
 * @filesource
 * @copyright     Copyright 2005-2008, Cake Software Foundation, Inc. (http://www.cakefoundation.org)
 * @link          http://www.cakefoundation.org/projects/info/cakephp CakePHP(tm) Project
 * @package       cake
 * @subpackage    cake.app.config
 * @since         CakePHP(tm) v 0.2.9
 * @version       $Revision: 7945 $
 * @modifiedby    $LastChangedBy: gwoo $
 * @lastmodified  $Date: 2008-12-19 02:16:01 +0000 (Fri, 19 Dec 2008) $
 * @license       http://www.opensource.org/licenses/mit-license.php The MIT License
 */

  Router::parseExtensions('json');

  Router::connect('/', array('controller' => 'pages', 'action' => 'home'));

  Router::connect('/mmenu', array('controller' => 'pages', 'action' => 'mmenu'));
  Router::connect('/megamenu', array('controller' => 'pages', 'action' => 'megamenu'));

  Router::connect('/cookies', array('plugin'=>'cookies', 'controller' => 'cookies', 'action' => 'preferences'));

  Router::connect('/destinations', array('controller' => 'destinations', 'action' => 'view', 'section' => 'destinations'));
  Router::connect('/destinations/healthcheck', array('controller' => 'destinations', 'action' => 'healthcheck', 'section' => 'destinations'));
  Router::connect('/destinations/:destination_slug', array('controller' => 'destinations', 'action' => 'view', 'section' => 'destinations'));

  Router::connect('/destinations/:destination_slug/activities/*', array('controller' => 'activities', 'action' => 'index', 'section' => 'destinations'));
  Router::connect('/destinations/:destination_slug/activity/:activity_slug', array('controller' => 'activities', 'action' => 'view', 'section' => 'destinations'));

  Router::connect('/destinations/:destination_slug/images/*', array('controller' => 'images', 'action' => 'index', 'section' => 'destinations'));
  Router::connect('/destinations/:destination_slug/videos/*', array('plugin' => 'gdata', 'controller' => 'youtube_videos', 'action' => 'index', 'section' => 'destinations'));

  Router::connect('/destinations/:destination_slug/itineraries/*', array('controller' => 'itineraries', 'action' => 'index', 'section' => 'destinations'));
  Router::connect('/destinations/:destination_slug/itinerary/:itinerary_slug', array('controller' => 'itineraries', 'action' => 'view', 'section' => 'destinations'));

  Router::connect('/destinations/:destination_slug/accommodation/:accommodation_slug', array('controller' => 'accommodations', 'action' => 'view', 'section' => 'destinations'), array('accommodation_slug' => '[a-zA-Z0-9_]+'));
  Router::connect('/destinations/:destination_slug/accommodation/*', array('controller' => 'accommodations', 'action' => 'index', 'section' => 'destinations'));

  Router::connect('/destinations/:destination_slug/holidays/:holiday_type_slug', array('controller' => 'holiday_types', 'action' => 'view', 'section' => 'destinations'), array('holiday_type_slug' => '[a-zA-Z0-9_]+'));
  Router::connect('/destinations/:destination_slug/holidays/*', array('controller' => 'holiday_types', 'action' => 'index', 'section' => 'destinations'), array('destination_slug' => '[a-zA-Z0-9_]+'));

  Router::connect('/destinations/:destination_slug/testimonials/*', array('plugin' => null, 'controller' => 'testimonials', 'action' => 'index', 'section' => 'destinations'));
  Router::connect('/destinations/:destination_slug/testimonial/:testimonial_slug', array('plugin' => null, 'controller' => 'testimonials', 'action' => 'view', 'section' => 'destinations'), array('testimonial_slug' => '[a-zA-Z0-9_]+'));


  Router::connect('/holidays/:holiday_type_slug/destinations/:destination_slug', array('controller' => 'destinations', 'action' => 'view', 'section' => 'holidays'), array('destination_slug' => '[a-zA-Z0-9_]+'));
  Router::connect('/holidays/:holiday_type_slug/destinations/*', array('controller' => 'destinations', 'action' => 'index', 'section' => 'holidays'));

  Router::connect('/holidays/:holiday_type_slug/accommodation/:accommodation_slug', array('controller' => 'accommodations', 'action' => 'view', 'section' => 'holidays'), array('accommodation_slug' => '[a-zA-Z0-9_]+'));
  Router::connect('/holidays/:holiday_type_slug/accommodation/*', array('controller' => 'accommodations', 'action' => 'index', 'section' => 'holidays'));

  Router::connect('/holidays/:holiday_type_slug/activities/*', array('controller' => 'activities', 'action' => 'index', 'section' => 'holidays'));
  Router::connect('/holidays/:holiday_type_slug/activity/:activity_slug', array('controller' => 'activities', 'action' => 'view', 'section' => 'holidays'), array('activity_slug' => '[a-zA-Z0-9_]+'));

  Router::connect('/holidays/:holiday_type_slug/itineraries/*', array('controller' => 'itineraries', 'action' => 'index', 'section' => 'holidays'));
  Router::connect('/holidays/:holiday_type_slug/itinerary/:itinerary_slug', array('controller' => 'itineraries', 'action' => 'view', 'section' => 'holidays'), array('itinerary_slug' => '[a-zA-Z0-9_]+'));

  Router::connect('/holidays/:holiday_type_slug/videos/*', array('plugin' => 'gdata', 'controller' => 'youtube_videos', 'action' => 'index', 'section' => 'holidays'));

  Router::connect('/holidays/:holiday_type_slug/testimonials/*', array('plugin' => null, 'controller' => 'testimonials', 'action' => 'index', 'section' => 'holidays'));
  Router::connect('/holidays/:holiday_type_slug/testimonial/:testimonial_slug', array('plugin' => null, 'controller' => 'testimonials', 'action' => 'view', 'section' => 'holidays'), array('testimonial_slug' => '[a-zA-Z0-9_]+'));

  Router::connect('/holidays', array('controller' => 'holiday_types', 'action' => 'index', 'section' => 'holidays'));
  Router::connect('/holidays/:holiday_type_slug', array('controller' => 'holiday_types', 'action' => 'view', 'section' => 'holidays'), array('holiday_type_slug' => '[a-zA-Z0-9_]+'));

  Router::connect('/page/testimonials', array('controller' => 'testimonials', 'action' => 'index'));

  Router::connect('/page/the_bon_voyage_holiday_promise', ['controller' => 'promise', 'action' => 'index']);

  Router::connect('/testimonial/:testimonial_slug', array('controller' => 'testimonials', 'action' => 'view', 'section' => 'testimonials'));
  Router::connect('/testimonial/:testimonial_slug', array('controller' => 'testimonials', 'action' => 'view'));

  Router::connect('/whats_hot', array('controller' => 'spotlights'));
  Router::connect('/whats_hot/:spotlight_slug', array('controller' => 'spotlights', 'action' => 'view'));

  // Canonical Pages
  Router::connect('/accommodations/:accommodation_slug', array('controller' => 'accommodations', 'action' => 'view', 'section' => 'destinations'), array('accommodation_slug' => '[a-zA-Z0-9_]+'));
  Router::connect('/activities/:activity_slug', array('controller' => 'activities', 'action' => 'view', 'section' => 'destinations'), array('activity_slug' => '[a-zA-Z0-9_]+'));
  Router::connect('/itineraries/:itinerary_slug', array('controller' => 'itineraries', 'action' => 'view', 'section' => 'destinations'), array('itinerary_slug' => '[a-zA-Z0-9_]+'));
  Router::connect('/testimonials/:testimonial_slug', array('controller' => 'testimonials', 'action' => 'view', 'section' => 'destinations'), array('testimonial_slug' => '[a-zA-Z0-9_]+'));

  Router::connect('/img/uploads/:id:separator:version.:extension', array('controller' => 'images', 'action' => 'cache_and_serve'), array('id' => '[0-9]+', 'separator' => '_', 'version' => '[^.]+', 'extension' => 'jpg|png|jpeg|gif'));

  Router::connect('/search/:term/*', array('plugin' => 'site_search', 'controller' => 'search', 'action' => 'results'));
  Router::connect('/search', array('plugin' => 'site_search', 'controller' => 'search', 'action' => 'results'));

  Router::connect('/page/:page_slug', array('controller' => 'pages', 'action' => 'view'));

  Router::connect('/faqs', array('plugin' => 'just_faqs', 'controller' => 'faqs', 'action' => 'index'));

  Router::connect('/webadmin/faqs/:action/*', array('plugin' => 'just_faqs', 'controller' => 'faqs', 'webadmin' => true));

  Router::connect('/contact_us', array('controller' => 'contacts', 'action' => 'contact_form'));

  Router::connect('/quote', array('controller' => 'contacts', 'action' => 'request_a_quote'));

  Router::connect('/call_back', array('controller' => 'contacts', 'action' => 'call_back_request'));

  // Far & Wide section routes - nested structure
  Router::connect('/far_wide/:parent_slug/:landing_page_slug', array('controller' => 'landing_pages', 'action' => 'view', 'section' => 'far_wide'), array('parent_slug' => '[a-zA-Z0-9_]+', 'landing_page_slug' => '[a-zA-Z0-9_]+'));
  Router::connect('/far_wide/:parent_slug/:landing_page_slug/activities/*', array('controller' => 'activities', 'action' => 'index', 'section' => 'far_wide'));
  Router::connect('/far_wide/:parent_slug/:landing_page_slug/activity/:activity_slug', array('controller' => 'activities', 'action' => 'view', 'section' => 'far_wide'));
  Router::connect('/far_wide/:parent_slug/:landing_page_slug/itineraries/*', array('controller' => 'itineraries', 'action' => 'index', 'section' => 'far_wide'));
  Router::connect('/far_wide/:parent_slug/:landing_page_slug/itinerary/:itinerary_slug', array('controller' => 'itineraries', 'action' => 'view', 'section' => 'far_wide'));
  Router::connect('/far_wide/:parent_slug/:landing_page_slug/images/*', array('controller' => 'images', 'action' => 'index', 'section' => 'far_wide'));
  Router::connect('/far_wide/:parent_slug/:landing_page_slug/videos/*', array('plugin' => 'gdata', 'controller' => 'youtube_videos', 'action' => 'index', 'section' => 'far_wide'));
  Router::connect('/far_wide/:parent_slug/:landing_page_slug/accommodation/:accommodation_slug', array('controller' => 'accommodations', 'action' => 'view', 'section' => 'far_wide'), array('accommodation_slug' => '[a-zA-Z0-9_]+'));
  Router::connect('/far_wide/:parent_slug/:landing_page_slug/accommodation/*', array('controller' => 'accommodations', 'action' => 'index', 'section' => 'far_wide'));
  Router::connect('/far_wide/:parent_slug/:landing_page_slug/holidays/:holiday_type_slug', array('controller' => 'holiday_types', 'action' => 'view', 'section' => 'far_wide'), array('holiday_type_slug' => '[a-zA-Z0-9_]+'));
  Router::connect('/far_wide/:parent_slug/:landing_page_slug/holidays/*', array('controller' => 'holiday_types', 'action' => 'index', 'section' => 'far_wide'));

  // Far & Wide section routes - single level
  Router::connect('/far_wide/:landing_page_slug', array('controller' => 'landing_pages', 'action' => 'view', 'section' => 'far_wide'), array('landing_page_slug' => '[a-zA-Z0-9_]+'));
  Router::connect('/far_wide/:landing_page_slug/activities/*', array('controller' => 'activities', 'action' => 'index', 'section' => 'far_wide'));
  Router::connect('/far_wide/:landing_page_slug/activity/:activity_slug', array('controller' => 'activities', 'action' => 'view', 'section' => 'far_wide'));
  Router::connect('/far_wide/:landing_page_slug/itineraries/*', array('controller' => 'itineraries', 'action' => 'index', 'section' => 'far_wide'));
  Router::connect('/far_wide/:landing_page_slug/itinerary/:itinerary_slug', array('controller' => 'itineraries', 'action' => 'view', 'section' => 'far_wide'));
  Router::connect('/far_wide/:landing_page_slug/images/*', array('controller' => 'images', 'action' => 'index', 'section' => 'far_wide'));
  Router::connect('/far_wide/:landing_page_slug/videos/*', array('plugin' => 'gdata', 'controller' => 'youtube_videos', 'action' => 'index', 'section' => 'far_wide'));
  Router::connect('/far_wide/:landing_page_slug/accommodation/:accommodation_slug', array('controller' => 'accommodations', 'action' => 'view', 'section' => 'far_wide'), array('accommodation_slug' => '[a-zA-Z0-9_]+'));
  Router::connect('/far_wide/:landing_page_slug/accommodation/*', array('controller' => 'accommodations', 'action' => 'index', 'section' => 'far_wide'));
  Router::connect('/far_wide/:landing_page_slug/holidays/:holiday_type_slug', array('controller' => 'holiday_types', 'action' => 'view', 'section' => 'far_wide'), array('holiday_type_slug' => '[a-zA-Z0-9_]+'));
  Router::connect('/far_wide/:landing_page_slug/holidays/*', array('controller' => 'holiday_types', 'action' => 'index', 'section' => 'far_wide'));
  Router::connect('/far_wide/*', array('controller' => 'landing_pages', 'action' => 'index', 'section' => 'far_wide'));

  // Campaigns section routes (existing)
  Router::connect('/campaigns/:landing_page_slug', array('controller' => 'landing_pages', 'action' => 'view', 'section' => 'landing_pages'), array('landing_page_slug' => '[a-zA-Z0-9_]+'));
  Router::connect('/campaigns/:landing_page_slug/activities/*', array('controller' => 'activities', 'action' => 'index', 'section' => 'landing_pages'));
  Router::connect('/campaigns/:landing_page_slug/activity/:activity_slug', array('controller' => 'activities', 'action' => 'view', 'section' => 'landing_pages'));
  Router::connect('/campaigns/:landing_page_slug/itineraries/*', array('controller' => 'itineraries', 'action' => 'index', 'section' => 'landing_pages'));
  Router::connect('/campaigns/:landing_page_slug/itinerary/:itinerary_slug', array('controller' => 'itineraries', 'action' => 'view', 'section' => 'landing_pages'));
  Router::connect('/campaigns/:landing_page_slug/images/*', array('controller' => 'images', 'action' => 'index', 'section' => 'landing_pages'));
  Router::connect('/campaigns/:landing_page_slug/videos/*', array('plugin' => 'gdata', 'controller' => 'youtube_videos', 'action' => 'index', 'section' => 'landing_pages'));
  Router::connect('/campaigns/:landing_page_slug/accommodation/:accommodation_slug', array('controller' => 'accommodations', 'action' => 'view', 'section' => 'landing_pages'), array('accommodation_slug' => '[a-zA-Z0-9_]+'));
  Router::connect('/campaigns/:landing_page_slug/accommodation/*', array('controller' => 'accommodations', 'action' => 'index', 'section' => 'landing_pages'));
  Router::connect('/campaigns/:landing_page_slug/holidays/:holiday_type_slug', array('controller' => 'holiday_types', 'action' => 'view', 'section' => 'landing_pages'), array('holiday_type_slug' => '[a-zA-Z0-9_]+'));
  Router::connect('/campaigns/:landing_page_slug/holidays/*', array('controller' => 'holiday_types', 'action' => 'index', 'section' => 'landing_pages'));
  Router::connect('/campaigns/*', array('controller' => 'landing_pages', 'action' => 'index', 'section' => 'landing_pages'));

  Router::connect('/in_the_press/:press_release_slug', array(
    'controller' => 'press_releases',
    'action'     => 'view',
  ), array(
    'press_release_slug' => '[a-zA-Z0-9_]+'
  ));

  Router::connect('/in_the_press/*', array(
    'controller' => 'press_releases',
    'action'     => 'index'
  ));

  Router::connect('/start_planning_now', [
    'controller' => 'travel_plans',
    'action' => 'add',
  ]);

  Router::connect('/social', [
    'controller' => 'travel_plans',
    'action' => 'add_social',
  ]);

  Router::connect('/make_an_enquiry', [
    'controller' => 'travel_plans',
    'action' => 'add_lite',
  ]);

  Router::connect('/v1/*', array(
    'controller' => 'itineraries',
    'action'     => 'yahooGeoProxy'
  ));

  Router::connect('/proxy/:action', array(
    'controller' => 'proxy'
  ));

  Router::connect('/api/navigation/megamenu', array(
    'controller' => 'navigation',
    'action' => 'megamenu'
  ));

  Router::connect('/api/navigation/mmenu', array(
    'controller' => 'navigation',
    'action' => 'mmenu'
  ));

  Router::connect('/webadmin', array('webadmin' => true, 'controller' => 'users', 'action' => 'home'));

  include(APP.'plugins'.DS.'downloads'.DS.'config'.DS.'routes.php');
?>
