<?php
  echo $this->element('modules/page_content_header', array(
    'header' => $navigationMenu['NavigationMenu']['name']
  ));
?>

<section class="page-content-body js-page-content-body">
  <div class="page-content-body__inner">
    <div class="page-content-body__content">
      <?php
        echo $this->element('content_blocks', array(
          'contentBlocks' => $contentBlocks,
          'modifier'      => 'far-wide'
        ));
      ?>
    </div>

    <?php
      // Generate Far & Wide specific navigation for sidebar
      $farWideNavigation = array();

      // Get all Far & Wide menu items from database
      $navigationMenuModel = ClassRegistry::init('NavigationMenu');
      $farWideItems = $navigationMenuModel->find('threaded', array(
          'conditions' => array(
              'NavigationMenu.menu_type' => 'far_wide',
              'NavigationMenu.published' => 1
          ),
          'fields' => array(
              'NavigationMenu.id',
              'NavigationMenu.parent_id',
              'NavigationMenu.name',
              'NavigationMenu.url',
              'NavigationMenu.page_id'
          ),
          'order' => 'NavigationMenu.lft ASC',
          'recursive' => -1
      ));

      // Build navigation structure for sidebar
      if (!empty($farWideItems)) {
          $rootItem = $farWideItems[0]; // Far & Wide root
          $currentUrl = $navigationMenu['NavigationMenu']['url'];

          // Create Far & Wide navigation structure
          $farWideNavigation = array(
              array(
                  'text' => $rootItem['NavigationMenu']['name'],
                  'url' => false, // Non-clickable parent
                  'selected' => false,
                  'has_children' => !empty($rootItem['children']),
                  'children' => array()
              )
          );

          // Process children (South America, Worldwide)
          if (!empty($rootItem['children'])) {
              foreach ($rootItem['children'] as $child) {
                  $childUrl = $child['NavigationMenu']['url'];
                  $isSelected = ($childUrl === $currentUrl);

                  // Convert # URLs to false for non-clickable items
                  if ($childUrl === '#' || empty($childUrl)) {
                      $childUrl = false;
                  }

                  $childItem = array(
                      'text' => $child['NavigationMenu']['name'],
                      'url' => $childUrl,
                      'selected' => $isSelected,
                      'has_children' => !empty($child['children']),
                      'children' => array()
                  );

                  // Process grandchildren (Cruises, Escorted Tours under Worldwide)
                  if (!empty($child['children'])) {
                      foreach ($child['children'] as $grandchild) {
                          $grandchildUrl = $grandchild['NavigationMenu']['url'];
                          $isGrandchildSelected = ($grandchildUrl === $currentUrl);

                          // Convert # URLs to false for non-clickable items
                          if ($grandchildUrl === '#' || empty($grandchildUrl)) {
                              $grandchildUrl = false;
                          }

                          $childItem['children'][] = array(
                              'text' => $grandchild['NavigationMenu']['name'],
                              'url' => $grandchildUrl,
                              'selected' => $isGrandchildSelected
                          );

                          // If grandchild is selected, mark parent as having selected child
                          if ($isGrandchildSelected) {
                              $childItem['selected'] = true;
                          }
                      }
                  }

                  $farWideNavigation[0]['children'][] = $childItem;
              }
          }
      }

      // Pass Far & Wide navigation to sidebar
      echo $this->element('sidebar', array(
          'modifier' => 'far-wide',
          'navigation' => $farWideNavigation
      ));
    ?>
  </div>
</section>

<?php
  // Set up meta tags from the page
  if (!empty($page['meta_description'])) {
    $this->set('metaDescription', $page['meta_description']);
  }

  if (!empty($page['meta_keywords'])) {
    $this->set('metaKeywords', $page['meta_keywords']);
  }
?>
