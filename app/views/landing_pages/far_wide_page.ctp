<?php
  echo $this->element('modules/page_content_header', array(
    'header' => $navigationMenu['NavigationMenu']['name']
  ));
?>

<section class="page-content-body js-page-content-body">
  <div class="page-content-body__inner">
    <div class="page-content-body__content">
      
      <?php if (!empty($contentBlocks)): ?>
        <div class="content-blocks content-blocks--far-wide">
          <?php foreach ($contentBlocks as $block): ?>
            <div class="content-block">
              <?php if (!empty($block['ContentBlock']['content'])): ?>
                <div class="content-block__text">
                  <?php echo $block['ContentBlock']['content']; ?>
                </div>
              <?php endif; ?>
              
              <?php if (!empty($block['ContentBlock']['image_id'])): ?>
                <div class="content-block__image">
                  <?php
                    $image = ClassRegistry::init('Image')->findById($block['ContentBlock']['image_id']);
                    if (!empty($image)) {
                      echo $this->element('image', array(
                        'image' => $image['Image'],
                        'version' => 'crop370x370'
                      ));
                    }
                  ?>
                </div>
              <?php endif; ?>
              
              <?php if (!empty($block['ContentBlock']['link']) && !empty($block['ContentBlock']['link_text'])): ?>
                <div class="content-block__link">
                  <?php echo $html->link($block['ContentBlock']['link_text'], $block['ContentBlock']['link']); ?>
                </div>
              <?php endif; ?>
            </div>
          <?php endforeach; ?>
        </div>
      <?php else: ?>
        <div class="content-blocks content-blocks--far-wide">
          <div class="content-block">
            <div class="content-block__text">
              <p>Content for this Far & Wide section is coming soon.</p>
            </div>
          </div>
        </div>
      <?php endif; ?>
      
    </div>
  </div>
</section>

<?php
  // Set up meta tags from the page
  if (!empty($page['meta_description'])) {
    $this->set('metaDescription', $page['meta_description']);
  }
  
  if (!empty($page['meta_keywords'])) {
    $this->set('metaKeywords', $page['meta_keywords']);
  }
?>
