<?php
  echo $this->element('modules/page_content_header', array(
    'header' => $navigationMenu['NavigationMenu']['name']
  ));
?>

<section class="page-content-body js-page-content-body">
  <div class="page-content-body__inner">
    <div class="page-content-body__content">
      <?php
        echo $this->element('content_blocks', array(
          'contentBlocks' => $contentBlocks,
          'modifier'      => 'far-wide'
        ));
      ?>
    </div>

    <?php
      // For now, use the same sidebar as regular pages
      // TODO: Implement Far & Wide specific navigation
      echo $this->element('sidebar', array(
        'modifier' => 'far-wide'
      ));
    ?>
  </div>
</section>

<?php
  // Set up meta tags from the page
  if (!empty($page['meta_description'])) {
    $this->set('metaDescription', $page['meta_description']);
  }

  if (!empty($page['meta_keywords'])) {
    $this->set('metaKeywords', $page['meta_keywords']);
  }
?>
