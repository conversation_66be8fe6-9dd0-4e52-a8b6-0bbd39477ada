<?php
  echo $this->element('modules/page_content_header', array(
    'header' => $navigationMenu['NavigationMenu']['name']
  ));
?>

<section class="page-content-body js-page-content-body">
  <div class="page-content-body__inner">
    <div class="page-content-body__content">
      <?php
        echo $this->element('content_blocks', array(
          'contentBlocks' => $contentBlocks,
          'modifier'      => 'far-wide'
        ));
      ?>
    </div>

    <?php
      // Generate contextual navigation for sidebar based on current page position
      $farWideNavigation = array();

      // Get all Far & Wide menu items from database
      $navigationMenuModel = ClassRegistry::init('NavigationMenu');
      $farWideItems = $navigationMenuModel->find('threaded', array(
          'conditions' => array(
              'NavigationMenu.menu_type' => 'far_wide',
              'NavigationMenu.published' => 1
          ),
          'fields' => array(
              'NavigationMenu.id',
              'NavigationMenu.parent_id',
              'NavigationMenu.name',
              'NavigationMenu.url',
              'NavigationMenu.page_id'
          ),
          'order' => 'NavigationMenu.lft ASC',
          'recursive' => -1
      ));

      // Build contextual navigation structure for sidebar
      if (!empty($farWideItems)) {
          $currentUrl = $navigationMenu['NavigationMenu']['url'];
          $currentMenuId = $navigationMenu['NavigationMenu']['id'];

          // Find the current menu item and its context
          $currentItem = null;
          $parentItem = null;
          $siblings = array();

          // Search through the hierarchy to find current item and its context
          $rootItem = $farWideItems[0]; // Far & Wide root

          // Check if current item is a direct child of root (e.g., South America)
          if (!empty($rootItem['children'])) {
              foreach ($rootItem['children'] as $child) {
                  if ($child['NavigationMenu']['id'] == $currentMenuId) {
                      // Current item is a top-level section (e.g., South America)
                      $currentItem = $child;
                      $siblings = $rootItem['children']; // All top-level sections
                      break;
                  }

                  // Check if current item is a grandchild (e.g., Cruises under Worldwide)
                  if (!empty($child['children'])) {
                      foreach ($child['children'] as $grandchild) {
                          if ($grandchild['NavigationMenu']['id'] == $currentMenuId) {
                              // Current item is under a parent section (e.g., Cruises under Worldwide)
                              $currentItem = $grandchild;
                              $parentItem = $child;
                              $siblings = $child['children']; // Siblings under the same parent
                              break 2;
                          }
                      }
                  }
              }
          }

          // Build navigation based on context
          if ($currentItem && $parentItem) {
              // Current page is a child item (e.g., Cruises) - show parent and siblings
              $parentUrl = $parentItem['NavigationMenu']['url'];
              if ($parentUrl === '#' || empty($parentUrl)) {
                  $parentUrl = false;
              }

              $farWideNavigation = array(
                  array(
                      'text' => $parentItem['NavigationMenu']['name'],
                      'url' => $parentUrl,
                      'selected' => false,
                      'has_children' => true,
                      'children' => array()
                  )
              );

              // Add siblings (including current item)
              foreach ($siblings as $sibling) {
                  $siblingUrl = $sibling['NavigationMenu']['url'];
                  $isSelected = ($sibling['NavigationMenu']['id'] == $currentMenuId);

                  if ($siblingUrl === '#' || empty($siblingUrl)) {
                      $siblingUrl = false;
                  }

                  $farWideNavigation[0]['children'][] = array(
                      'text' => $sibling['NavigationMenu']['name'],
                      'url' => $siblingUrl,
                      'selected' => $isSelected
                  );
              }
          } elseif ($currentItem) {
              // Current page is a top-level section (e.g., South America) - show all top-level sections
              $farWideNavigation = array(
                  array(
                      'text' => $rootItem['NavigationMenu']['name'],
                      'url' => false,
                      'selected' => false,
                      'has_children' => true,
                      'children' => array()
                  )
              );

              foreach ($siblings as $sibling) {
                  $siblingUrl = $sibling['NavigationMenu']['url'];
                  $isSelected = ($sibling['NavigationMenu']['id'] == $currentMenuId);

                  if ($siblingUrl === '#' || empty($siblingUrl)) {
                      $siblingUrl = false;
                  }

                  $siblingItem = array(
                      'text' => $sibling['NavigationMenu']['name'],
                      'url' => $siblingUrl,
                      'selected' => $isSelected,
                      'has_children' => !empty($sibling['children']),
                      'children' => array()
                  );

                  // Add children if this sibling has them and is selected
                  if ($isSelected && !empty($sibling['children'])) {
                      foreach ($sibling['children'] as $child) {
                          $childUrl = $child['NavigationMenu']['url'];
                          if ($childUrl === '#' || empty($childUrl)) {
                              $childUrl = false;
                          }

                          $siblingItem['children'][] = array(
                              'text' => $child['NavigationMenu']['name'],
                              'url' => $childUrl,
                              'selected' => false
                          );
                      }
                  }

                  $farWideNavigation[0]['children'][] = $siblingItem;
              }
          }
      }

      // Pass contextual navigation to sidebar
      echo $this->element('sidebar', array(
          'modifier' => 'far-wide',
          'navigation' => $farWideNavigation
      ));
    ?>
  </div>
</section>

<?php
  // Set up meta tags from the page
  if (!empty($page['meta_description'])) {
    $this->set('metaDescription', $page['meta_description']);
  }

  if (!empty($page['meta_keywords'])) {
    $this->set('metaKeywords', $page['meta_keywords']);
  }
?>
