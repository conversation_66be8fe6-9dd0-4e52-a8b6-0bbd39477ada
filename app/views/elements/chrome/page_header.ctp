<?php
// Transform the data structures
// First, transform USA destinations if they're not in the correct format
$usaDestinations = array_map(function ($dest) {
    return array(
        'Destination' => isset($dest['Destination']) ? $dest['Destination'] : $dest
    );
}, $usaDestinations ?: array());

$canadaDestinations = array_map(function ($dest) {
    return array(
        'Destination' => isset($dest['Destination']) ? $dest['Destination'] : $dest
    );
}, $canadaDestinations ?: array());

$holidayTypes = array_map(function ($type) {
    return isset($type['HolidayType']) ? $type : array('HolidayType' => $type);
}, $holidayTypes ?: array());

// Load data from cache if the variables are empty
if (empty($holidayInfoPages) || empty($aboutPages)) {
    $cache = new Cache();
    $cachedData = $cache->read('main_navigation', 'navigation');
    if ($cachedData) {
        if (empty($holidayInfoPages) && isset($cachedData['holidayInfoPages'])) {
            $holidayInfoPages = $cachedData['holidayInfoPages'];
        }
        if (empty($aboutPages) && isset($cachedData['aboutPages'])) {
            $aboutPages = $cachedData['aboutPages'];
        }
    }
}

?>
<header class="page-header">
    <div class="page-header__inner">
        <!-- <div class="desktop-nav-wrap"> -->
            <a class="page-header__logo" href="/">
                USA &amp; Canada Holidays - Bon Voyage
            </a>
            <button class="nav-toggle mmenu-trigger" aria-label="Toggle mobile menu" aria-expanded="false">
                <img src="/img/site/icons/hamburger.svg" alt="Menu" class="hamburger-icon">
                <img src="/img/site/icons/cross.svg" alt="Close" class="cross-icon" style="display: none;">
            </button>
            <!-- Mobile Menu -->
            <nav id="mobile-menu" role="navigation" aria-label="Mobile navigation">
                <?php
                // Build comprehensive navigation structure
                $mobileNavigation = array();
                // Add USA section with preserved order from navigation component
                if (!empty($mainNav['usa']['items'])) {
                    $mobileNavigation[] = array(
                        'text' => 'USA',
                        'url' => '/destinations/usa_holidays',
                        'has_children' => true,
                        'children' => array_map(function ($dest) {
                            $item = array(
                                'text' => $dest['Destination']['name'],
                                'url' => '/destinations/' . $dest['Destination']['slug']
                            );
                            // Add child destinations if they exist
                            if (!empty($dest['Destination']['children'])) {
                                $item['has_children'] = true;
                                $item['children'] = array_map(function ($child) {
                                    return array(
                                        'text' => $child['Destination']['name'],
                                        'url' => '/destinations/' . $child['Destination']['slug']
                                    );
                                }, $dest['Destination']['children']);
                            }
                            return $item;
                        }, $mainNav['usa']['items'])
                    );
                }
                // Add Canada section with preserved order from navigation component
                if (!empty($mainNav['canada']['items'])) {
                    $mobileNavigation[] = array(
                        'text' => 'Canada',
                        'url' => '/destinations/canada_holidays',
                        'has_children' => true,
                        'children' => array_map(function ($dest) {
                            $item = array(
                                'text' => $dest['Destination']['name'],
                                'url' => '/destinations/' . $dest['Destination']['slug']
                            );
                            // Add child destinations if they exist
                            if (!empty($dest['Destination']['children'])) {
                                $item['has_children'] = true;
                                $item['children'] = array_map(function ($child) {
                                    return array(
                                        'text' => $child['Destination']['name'],
                                        'url' => '/destinations/' . $child['Destination']['slug']
                                    );
                                }, $dest['Destination']['children']);
                            }
                            return $item;
                        }, $mainNav['canada']['items'])
                    );
                }

                // Add Far & Wide section after Canada (using dynamic menu items)
                if (!empty($farWideMenuItems)) {
                    $mobileNavigation[] = array(
                        'text' => 'Far & Wide',
                        'url' => false,  // Use false instead of '#' so it renders as plain text
                        'has_children' => true,
                        'children' => $farWideMenuItems
                    );
                }

                // Add Holiday Types section with preserved order from navigation component
                if (!empty($holidayTypes)) {
                    $mobileNavigation[] = array(
                        'text' => 'Holiday Types',
                        'url' => '/holidays',
                        'has_children' => true,
                        'children' => array_map(function ($type) {
                            return array(
                                'text' => $type['HolidayType']['name'],
                                'url' => '/holidays/' . $type['HolidayType']['slug']
                            );
                        }, $holidayTypes)
                    );
                }
                // Add What's Hot section with preserved order from navigation component
                if (!empty($whatsHot)) {
                    $mobileNavigation[] = array(
                        'text' => "What's Hot",
                        'url' => '/spotlights',
                        'has_children' => true,
                        'children' => array_map(function ($hot) {
                            return array(
                                'text' => $hot['Spotlight']['name'],
                                'url' => '/spotlights/' . $hot['Spotlight']['slug']
                            );
                        }, $whatsHot)
                    );
                }

                // Add About Us section with preserved order from navigation component
                if (!empty($aboutPages)) {
                    $mobileNavigation[] = array(
                        'text' => 'About Us',
                        'url' => '/page/about_bon_voyage',
                        'has_children' => true,
                        'children' => array_map(function ($page) {
                            return array(
                                'text' => $page['Page']['internal_ref'],
                                'url' => '/page/' . $page['Page']['slug']
                            );
                        }, $aboutPages)
                    );
                }
                // Add primary nav items
                $mobileNavigation[] = array(
                    'text' => 'Holiday Info',
                    'url' => '/page/holiday_information'
                );
                $mobileNavigation[] = array(
                    'text' => 'Blog',
                    'url' => '/blog'
                );
                $mobileNavigation[] = array(
                    'text' => 'FAQs',
                    'url' => '/faqs'
                );
                $mobileNavigation[] = array(
                    'text' => 'Make an Enquiry',
                    'url' => '/make_an_enquiry'
                );
                // Output the mobile menu with preserved order
                if (!empty($mobileNavigation)) {
                    $options = array(
                        'hideParents' => false,
                        'url' => array('plugin' => null),
                        'itemOptions' => array(
                            'hasChildrenClass' => 'has_children',
                            'listClass' => 'mm-listview',
                            'itemClass' => 'mm-listitem',
                            'linkClass' => 'mm-listitem__text',
                            'linkAttributes' => array(
                                'role' => 'menuitem',
                                'tabindex' => '0'
                            ),
                            'listAttributes' => array(
                                'role' => 'menu'
                            )
                        )
                    );
                    $menuHtml = $app->menu($mobileNavigation, $options);
                    echo $menuHtml;
                }
                ?>
            </nav>
            <button class="search-toggle" data-toggle="search"><img src="/img/site/icons/search.svg" alt="Search" class="active"></button>
            <!-- Desktop Menu -->
            <nav class="desktop-menu hidden-xs">
                <div class="desktop-menu__inner">
                    <!-- Primary Navigation -->
                    <?php echo $this->element('chrome/primary_nav'); ?>
                    <!-- Secondary Navigation -->
                    <?php echo $this->element('chrome/secondary_nav'); ?>
                </div>
            </nav>
        <!-- </div> -->
        <div class="primary-search primary-search--page-header" id="search">
            <form class="primary-search__form" action="/search" method="get" id="header-search-form">
                <fieldset>

                    <div class="primary-search__input">
                        <input type="text" name="search" id="header-search-input" placeholder="Search">
                        <div class="button-wrap"><button type="submit">Submit</button></div>

                    </div>


                </fieldset>
            </form>

            <script>
            (function() {
                // Immediately capture the header search form to prevent other scripts from interfering
                var form = document.getElementById('header-search-form');
                var searchInput = document.getElementById('header-search-input');

                if (form && searchInput) {
                    // Remove any existing event listeners by cloning the form
                    var newForm = form.cloneNode(true);
                    form.parentNode.replaceChild(newForm, form);

                    // Get references to the new elements
                    var newSearchInput = document.getElementById('header-search-input');

                    // Add our event listener with high priority
                    newForm.addEventListener('submit', function(e) {
                        e.preventDefault();
                        e.stopImmediatePropagation();

                        var searchTerm = newSearchInput.value.trim();
                        if (searchTerm) {
                            // Use + for spaces instead of %20 to avoid 403 errors
                            var encodedTerm = encodeURIComponent(searchTerm).replace(/%20/g, '+');
                            window.location.href = '/search/' + encodedTerm;
                        }
                        return false;
                    }, true); // Use capture phase for higher priority

                    console.log('[Header Search Form] Clean URL redirect handler installed');
                }
            })();
            </script>
        </div>
        <!-- Mega Menu Dropdowns -->
        <div class="mega-menu">
            <!-- USA Dropdown -->
            <div class="mega-menu__panel" id="usa-dropdown">
                <div class="mega-menu__inner">
                    <?php if (!empty($mainNav['usa']['items'])): ?>
                        <div class="mega-menu__section">
                            <h3>Popular Destinations</h3>
                            <ul class="mega-menu__list mega-menu__list--columns">
                                <?php foreach ($mainNav['usa']['items'] as $dest): ?>
                                    <li class="mega-menu__item">
                                        <a href="/destinations/<?php echo h($dest['Destination']['slug']); ?>" class="mega-menu__link" title="<?php echo h($dest['Destination']['name']); ?>">
                                            <?php echo h($dest['Destination']['name']); ?>
                                        </a>
                                        <?php if (!empty($dest['Destination']['children'])): ?>
                                            <ul class="mega-menu__sublist">
                                                <?php foreach ($dest['Destination']['children'] as $child): ?>
                                                    <li class="mega-menu__subitem">
                                                        <a href="/destinations/<?php echo h($child['Destination']['slug']); ?>" class="mega-menu__sublink" title="<?php echo h($child['Destination']['name']); ?>">
                                                            <?php echo h($child['Destination']['name']); ?>
                                                        </a>
                                                    </li>
                                                <?php endforeach; ?>
                                            </ul>
                                        <?php endif; ?>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            <!-- Canada Dropdown -->
            <div class="mega-menu__panel" id="canada-dropdown">
                <div class="mega-menu__inner">
                    <?php if (!empty($mainNav['canada']['items'])): ?>
                        <div class="mega-menu__section">
                            <h3>Popular Destinations</h3>
                            <ul class="mega-menu__list mega-menu__list--columns">
                                <?php foreach ($mainNav['canada']['items'] as $dest): ?>
                                    <li class="mega-menu__item">
                                        <a href="/destinations/<?php echo h($dest['Destination']['slug']); ?>" class="mega-menu__link" title="<?php echo h($dest['Destination']['name']); ?>">
                                            <?php echo h($dest['Destination']['name']); ?>
                                        </a>
                                        <?php if (!empty($dest['Destination']['children'])): ?>
                                            <ul class="mega-menu__sublist">
                                                <?php foreach ($dest['Destination']['children'] as $child): ?>
                                                    <li class="mega-menu__subitem">
                                                        <a href="/destinations/<?php echo h($child['Destination']['slug']); ?>" class="mega-menu__sublink" title="<?php echo h($child['Destination']['name']); ?>">
                                                            <?php echo h($child['Destination']['name']); ?>
                                                        </a>
                                                    </li>
                                                <?php endforeach; ?>
                                            </ul>
                                        <?php endif; ?>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Far & Wide Dropdown -->
            <div class="mega-menu__panel" id="far-wide-dropdown">
                <div class="mega-menu__inner">
                    <?php echo $this->element('chrome/far_wide_megamenu', array('farWideMenuItems' => $farWideMenuItems)); ?>
                </div>
            </div>

            <!-- Holiday Types Dropdown -->
            <div class="mega-menu__panel" id="holiday-types-dropdown">
                <div class="mega-menu__inner">
                    <?php if (!empty($holidayTypes)): ?>
                        <div class="mega-menu__section">
                            <h3>Holiday Types</h3>
                            <ul class="mega-menu__list mega-menu__list--columns">
                                <?php foreach ($holidayTypes as $type): ?>
                                    <li class="mega-menu__item">
                                        <a href="/holidays/<?php echo h($type['HolidayType']['slug']); ?>" class="mega-menu__link" title="<?php echo h($type['HolidayType']['name']); ?>">
                                            <?php echo h($type['HolidayType']['name']); ?>
                                        </a>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- What's Hot Dropdown -->
            <div class="mega-menu__panel" id="whats-hot-dropdown">
                <div class="mega-menu__inner">
                    <?php if (!empty($whatsHot)): ?>
                        <div class="mega-menu__section">
                            <h3>What's Hot</h3>
                            <ul class="mega-menu__list mega-menu__list--columns">
                                <?php foreach ($whatsHot as $hot): ?>
                                    <li class="mega-menu__item">
                                        <a href="/spotlights/<?php echo h($hot['Spotlight']['slug']); ?>" class="mega-menu__link" title="<?php echo h($hot['Spotlight']['name']); ?>">
                                            <?php echo h($hot['Spotlight']['name']); ?>
                                        </a>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            <!-- Holiday Information Dropdown -->
            <div class="mega-menu__panel" id="holiday-info-dropdown">
                <div class="mega-menu__inner">
                    <?php if (!empty($holidayInfoPages)): ?>
                        <div class="mega-menu__section">
                            <ul class="mega-menu__list mega-menu__list--columns">
                                <?php foreach ($holidayInfoPages as $page): ?>
                                    <li class="mega-menu__item">
                                        <a href="/page/<?php echo h($page['Page']['slug']); ?>" class="mega-menu__link" title="<?php echo h($page['Page']['internal_ref']); ?>">
                                            <?php echo h($page['Page']['internal_ref']); ?>
                                        </a>
                                        <?php if (!empty($page['Page']['children'])): ?>
                                            <ul class="mega-menu__sublist">
                                                <?php foreach ($page['Page']['children'] as $child): ?>
                                                    <li class="mega-menu__subitem">
                                                        <a href="/page/<?php echo h($child['Page']['slug']); ?>" class="mega-menu__sublink" title="<?php echo h($child['Page']['internal_ref']); ?>">
                                                            <?php echo h($child['Page']['internal_ref']); ?>
                                                        </a>
                                                        <?php if (!empty($child['Page']['children'])): ?>
                                                            <ul class="mega-menu__sublist">
                                                                <?php foreach ($child['Page']['children'] as $grandchild): ?>
                                                                    <li class="mega-menu__subitem">
                                                                        <a href="/page/<?php echo h($grandchild['Page']['slug']); ?>" class="mega-menu__sublink" title="<?php echo h($grandchild['Page']['internal_ref']); ?>">
                                                                            <?php echo h($grandchild['Page']['internal_ref']); ?>
                                                                        </a>
                                                                        <?php if (!empty($grandchild['Page']['children'])): ?>
                                                                            <ul class="mega-menu__sublist">
                                                                                <?php foreach ($grandchild['Page']['children'] as $greatgrandchild): ?>
                                                                                    <li class="mega-menu__subitem">
                                                                                        <a href="/page/<?php echo h($greatgrandchild['Page']['slug']); ?>" class="mega-menu__sublink" title="<?php echo h($greatgrandchild['Page']['internal_ref']); ?>">
                                                                                            <?php echo h($greatgrandchild['Page']['internal_ref']); ?>
                                                                                        </a>
                                                                                    </li>
                                                                                <?php endforeach; ?>
                                                                            </ul>
                                                                        <?php endif; ?>
                                                                    </li>
                                                                <?php endforeach; ?>
                                                            </ul>
                                                        <?php endif; ?>
                                                    </li>
                                                <?php endforeach; ?>
                                            </ul>
                                        <?php endif; ?>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            <!-- About Dropdown -->
            <div class="mega-menu__panel" id="about-dropdown">
                <div class="mega-menu__inner">
                    <?php if (!empty($aboutPages)): ?>
                        <div class="mega-menu__section">
                            <ul class="mega-menu__list mega-menu__list--columns">
                                <?php foreach ($aboutPages as $page): ?>
                                    <li class="mega-menu__item">
                                        <a href="/page/<?php echo h($page['Page']['slug']); ?>" class="mega-menu__link" title="<?php echo h($page['Page']['internal_ref']); ?>">
                                            <?php echo h($page['Page']['internal_ref']); ?>
                                        </a>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        <!-- Mobile Search -->
            <!-- <div class="mobile-search">
            <form class="primary-search" action="/search" method="get">
                <input type="text" name="q" placeholder="Search...">
                <input type="submit" value="Search">
            </form>
                </div> -->

        <!-- Desktop Search -->
            <!-- <div class="desktop-search">
            <form class="primary-search" action="/search" method="get">
                <input type="text" name="q" placeholder="Search...">
                <input type="submit" value="Search">
            </form>
                </div> -->
</header>

<?php
// Debug output
// error_log("Holiday Info Pages in View: " . print_r($holidayInfoPages, true));
// error_log("About Pages in View: " . print_r($aboutPages, true));
?>

<!-- <pre>
Holiday Types:
<?php
// print_r($holidayTypes);
?>
</pre> -->

<?php
// error_log("=== Page Header Debug ===");
// error_log("Holiday types in header: " . (!empty($holidayTypes) ? count($holidayTypes) : 'EMPTY'));

// if (!empty($usaDestinations)) {
//     error_log("First USA destination: " . print_r($usaDestinations[0], true));
// }
?>

<?php
// debug($holidayTypes);
?>
