<?php if (empty($hideBreadcrumb) || $hideBreadcrumb !== true): ?>
  <div class="breadcrumb">
    <div class="breadcrumb__inner">
      <ul>
        <li><a href="/">Home</a></li>

        <?php
          if (!empty($breadcrumbs) && is_array($breadcrumbs)) {
            foreach ($breadcrumbs as $b) {
              if ($b === end($breadcrumbs)) {
                echo '<li>' . $b['text']. '</li>';
              } else {
                // Check if URL is valid and not just a placeholder
                if (!empty($b['url']) && $b['url'] !== '#' && $b['url'] !== false) {
                  echo '<li><a href="' . $b['url'] .'">' . $b['text'] . '</a></li>';
                } else {
                  echo '<li>' . $b['text'] . '</li>';
                }
              }
            }
          }
        ?>
      </ul>
    </div>
  </div>
<?php endif ?>
