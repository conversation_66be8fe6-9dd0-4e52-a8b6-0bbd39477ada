<?php
/**
 * Shared Far & Wide Megamenu Element
 * 
 * This element renders the Far & Wide dropdown section for both:
 * - Main site (page_header.ctp)
 * - API endpoint (mega_menu_api.ctp)
 * 
 * Expected variables:
 * - $farWideMenuItems: Array of Far & Wide menu items
 */
?>
<div class="mega-menu__section">
    <h3>Far & Wide</h3>
    <ul class="mega-menu__list mega-menu__list--columns">
        <?php if (!empty($farWideMenuItems)): ?>
            <?php foreach ($farWideMenuItems as $item): ?>
                <?php $hasChildren = !empty($item['children']); ?>
                <li class="mega-menu__item<?php echo $hasChildren ? ' has-children' : ''; ?>">
                    <?php if (!empty($item['url']) && $item['url'] !== '#' && $item['url'] !== false): ?>
                        <a href="<?php echo h($item['url']); ?>" class="mega-menu__link" title="<?php echo h($item['text']); ?>">
                            <?php echo h($item['text']); ?>
                        </a>
                    <?php else: ?>
                        <?php if ($hasChildren): ?>
                            <a href="#" class="mega-menu__link" title="<?php echo h($item['text']); ?>">
                                <?php echo h($item['text']); ?>
                            </a>
                        <?php else: ?>
                            <span class="mega-menu__text" title="<?php echo h($item['text']); ?>">
                                <?php echo h($item['text']); ?>
                            </span>
                        <?php endif; ?>
                    <?php endif; ?>
                    <?php if (!empty($item['children'])): ?>
                        <ul class="mega-menu__sublist">
                            <?php foreach ($item['children'] as $child): ?>
                                <li class="mega-menu__subitem">
                                    <?php if (!empty($child['url']) && $child['url'] !== '#' && $child['url'] !== false): ?>
                                        <a href="<?php echo h($child['url']); ?>" class="mega-menu__sublink" title="<?php echo h($child['text']); ?>">
                                            <?php echo h($child['text']); ?>
                                        </a>
                                    <?php else: ?>
                                        <span class="mega-menu__subtext" title="<?php echo h($child['text']); ?>">
                                            <?php echo h($child['text']); ?>
                                        </span>
                                    <?php endif; ?>
                                    <?php if (!empty($child['children'])): ?>
                                        <ul class="mega-menu__subsublist">
                                            <?php foreach ($child['children'] as $grandchild): ?>
                                                <li class="mega-menu__subsubitem">
                                                    <?php if (!empty($grandchild['url']) && $grandchild['url'] !== '#' && $grandchild['url'] !== false): ?>
                                                        <a href="<?php echo h($grandchild['url']); ?>" class="mega-menu__subsublink" title="<?php echo h($grandchild['text']); ?>">
                                                            <?php echo h($grandchild['text']); ?>
                                                        </a>
                                                    <?php else: ?>
                                                        <span class="mega-menu__subsubtext" title="<?php echo h($grandchild['text']); ?>">
                                                            <?php echo h($grandchild['text']); ?>
                                                        </span>
                                                    <?php endif; ?>
                                                </li>
                                            <?php endforeach; ?>
                                        </ul>
                                    <?php endif; ?>
                                </li>
                            <?php endforeach; ?>
                        </ul>
                    <?php endif; ?>
                </li>
            <?php endforeach; ?>
        <?php endif; ?>
    </ul>
</div>
