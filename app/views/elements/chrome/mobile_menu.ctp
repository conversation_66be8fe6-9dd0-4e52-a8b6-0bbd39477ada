<?php
/**
 * Shared Mobile Menu Element
 *
 * This element renders the mobile navigation menu for:
 * - Main site (page_header.ctp)
 * - API endpoint (navigation_controller.php -> mmenu_api.ctp)
 * - PagesController (pages_controller.php -> mmenu_api.ctp)
 *
 * Expected variables:
 * - $mobileNavigation: Array of mobile navigation items
 * - $renderType: 'html' (for main site) or 'api' (for API endpoints) - optional, defaults to 'api'
 */

// Default to API rendering if not specified
if (!isset($renderType)) {
    $renderType = 'api';
}

// Recursive function to render the mobile menu with proper mmenu structure and classes
function renderMobileMenu($items, $level = 0) {
    if (empty($items)) return '';

    $html = '<ul>';
    $count = count($items);

    foreach ($items as $index => $item) {
        $isFirst = ($index === 0);
        $isLast = ($index === $count - 1);

        $classes = array();
        if ($isFirst) $classes[] = 'first_child';
        if ($isLast) $classes[] = 'last_child';
        if (!empty($item['has_children'])) $classes[] = 'has_children';

        $html .= '<li' . (!empty($classes) ? ' class="' . implode(' ', $classes) . '"' : '') . '>';

        // Generate proper mmenu structure with separate <a> tags
        if (!empty($item['has_children']) && !empty($item['children'])) {
            // For items with children, create the main link and submenu button
            if (!empty($item['url']) && $item['url'] !== '#' && $item['url'] !== false) {
                // Item has both a URL and children
                $html .= '<a href="' . h($item['url']) . '" class="mm-listitem__text">' . h($item['text']) . '</a>';
            } else {
                // Item has children but no URL (like Far & Wide)
                $html .= '<a href="#" class="mm-listitem__text">' . h($item['text']) . '</a>';
            }
            // Add the submenu button
            $html .= '<a class="mm-btn mm-btn--next mm-listitem__btn" aria-label="Open submenu" href="#"></a>';

            // Add the submenu
            $html .= renderMobileMenu($item['children'], $level + 1);
        } else {
            // For items without children, just create a simple link
            $html .= '<a href="' . h($item['url']) . '" class="mm-listitem__text">' . h($item['text']) . '</a>';
        }

        $html .= '</li>';
    }

    $html .= '</ul>';
    return $html;
}

// Render the mobile menu based on the render type
if (!empty($mobileNavigation)) {
    if ($renderType === 'html') {
        // For main site - use the AppHelper menu method for consistency
        if (isset($this) && method_exists($this, 'getVar')) {
            $app = $this->getVar('app');
            if ($app && method_exists($app, 'menu')) {
                $options = array(
                    'hideParents' => false,
                    'url' => array('plugin' => null),
                    'itemOptions' => array(
                        'hasChildrenClass' => 'has_children',
                        'listClass' => 'mm-listview',
                        'itemClass' => 'mm-listitem',
                        'linkClass' => 'mm-listitem__text',
                        'linkAttributes' => array(
                            'role' => 'menuitem',
                            'tabindex' => '0'
                        ),
                        'listAttributes' => array(
                            'role' => 'menu'
                        )
                    )
                );
                echo $app->menu($mobileNavigation, $options);
            } else {
                // Fallback to simple rendering
                echo renderMobileMenu($mobileNavigation);
            }
        } else {
            // Fallback to simple rendering
            echo renderMobileMenu($mobileNavigation);
        }
    } else {
        // For API endpoints - use simple HTML rendering
        echo renderMobileMenu($mobileNavigation);
    }
}
?>
