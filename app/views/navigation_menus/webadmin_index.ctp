<div id="navigationMenusIndex" class="index">
  <div class="title clearfix">
    <h2><?php __('Navigation Menus');?></h2>
    <?php echo $this->element('webadmin_actions'); ?>
  </div>

  <?php $webAdmin->sessionFlash(); ?>

  <!-- Menu Type Filter -->
  <div class="filter-bar">
    <strong>Filter by Menu Type:</strong>
    <?php
    $menuTypeOptions = array('all' => 'All') + $menuTypes;
    foreach ($menuTypeOptions as $key => $label) {
        $class = ($key == $menuType) ? 'active' : '';
        echo $html->link($label, array('action' => 'webadmin_index', 'menu_type' => $key), array('class' => $class));
        echo ' | ';
    }
    ?>
  </div>

  <?php if (!empty($navigationMenus)): ?>
    <?php if (!empty($breadcrumbs)): ?>
      <?php echo $this->element('webadmin_breadcrumb'); ?>
    <?php endif; ?>

    <div class="table">
      <table cellpadding="0" cellspacing="0" id="navigationMenusTable">
        <thead>
          <tr>
            <th class="reorder"></th>
            <th>Name</th>
            <th>URL</th>
            <th>Menu Type</th>
            <th>Published</th>
            <th>Created</th>
            <th>Modified</th>
            <th class="actions"><?php __('Actions'); ?></th>
          </tr>
        </thead>
        <tbody>
          <?php
          // Render menu items hierarchically
          function renderMenuItems($items, $level = 0, $html) {
              $output = '';
              $i = 0;

              foreach ($items as $item) {
                  $class = '';
                  if ($i++ % 2 == 0) {
                      $class = ' class="altrow"';
                  }

                  $indent = str_repeat('&nbsp;&nbsp;&nbsp;', $level);
                  $published = $item['NavigationMenu']['published'] ? 'Yes' : 'No';

                  $output .= '<tr' . $class . '>';
                  $output .= '<td class="reorder">';
                  $output .= $html->link('↑', array('action' => 'webadmin_move_up', $item['NavigationMenu']['id']), array('title' => 'Move Up', 'escape' => false));
                  $output .= ' ';
                  $output .= $html->link('↓', array('action' => 'webadmin_move_down', $item['NavigationMenu']['id']), array('title' => 'Move Down', 'escape' => false));
                  $output .= '</td>';
                  $output .= '<td>' . $indent . h($item['NavigationMenu']['name']) . '</td>';
                  $output .= '<td>' . h($item['NavigationMenu']['url']) . '</td>';
                  $output .= '<td>' . h($item['NavigationMenu']['menu_type']) . '</td>';
                  $output .= '<td>' . $published . '</td>';
                  $output .= '<td>' . h($item['NavigationMenu']['created']) . '</td>';
                  $output .= '<td>' . h($item['NavigationMenu']['modified']) . '</td>';
                  $output .= '<td class="actions">';
                  $output .= $html->link(__('Edit', true), array('action' => 'webadmin_edit', $item['NavigationMenu']['id']));
                  $output .= ' ';
                  $output .= $html->link(__('Delete', true), array('action' => 'webadmin_delete', $item['NavigationMenu']['id']), null, sprintf(__('Are you sure you want to delete # %s?', true), $item['NavigationMenu']['id']));
                  $output .= '</td>';
                  $output .= '</tr>';

                  // Render children
                  if (!empty($item['children'])) {
                      $output .= renderMenuItems($item['children'], $level + 1, $html);
                  }
              }

              return $output;
          }

          echo renderMenuItems($navigationMenus, 0, $html);
          ?>
        </tbody>
      </table>
    </div>

  <?php else: ?>
    <p>No navigation menu items found.</p>
  <?php endif; ?>
</div>
