<?php

class TreeHelper extends <PERSON><PERSON><PERSON>elper {

    var $helpers = array('Html');

    /**
     * Generate HTML for a hierarchical tree structure
     * 
     * @param array $data Tree data (threaded format from CakePHP)
     * @param array $options Options for rendering
     * @return string HTML output
     */
    function generate($data, $options = array()) {
        $defaults = array(
            'element' => null,
            'callback' => null,
            'type' => 'ul',
            'id' => null,
            'class' => null,
            'itemClass' => null,
            'autoPath' => null
        );
        
        $options = array_merge($defaults, $options);
        
        if (empty($data)) {
            return '';
        }
        
        $out = '';
        
        if ($options['type'] == 'ul') {
            $out .= '<ul';
            if ($options['id']) {
                $out .= ' id="' . $options['id'] . '"';
            }
            if ($options['class']) {
                $out .= ' class="' . $options['class'] . '"';
            }
            $out .= '>';
            
            foreach ($data as $item) {
                $out .= $this->_generateItem($item, $options);
            }
            
            $out .= '</ul>';
        }
        
        return $out;
    }
    
    /**
     * Generate HTML for a single tree item
     * 
     * @param array $item Single item data
     * @param array $options Rendering options
     * @return string HTML output
     */
    function _generateItem($item, $options) {
        $out = '<li';
        
        if ($options['itemClass']) {
            $out .= ' class="' . $options['itemClass'] . '"';
        }
        
        $out .= '>';
        
        // Use callback if provided
        if ($options['callback'] && is_callable($options['callback'])) {
            $out .= call_user_func($options['callback'], $item);
        } else {
            // Default rendering
            $modelName = key($item);
            if (isset($item[$modelName]['name'])) {
                $out .= h($item[$modelName]['name']);
            } elseif (isset($item[$modelName]['title'])) {
                $out .= h($item[$modelName]['title']);
            } else {
                $out .= 'Item';
            }
        }
        
        // Render children if they exist
        if (isset($item['children']) && !empty($item['children'])) {
            $out .= $this->generate($item['children'], $options);
        }
        
        $out .= '</li>';
        
        return $out;
    }
    
    /**
     * Generate a select dropdown from tree data
     * 
     * @param array $data Tree data
     * @param array $options Options for the select
     * @return array Options array for FormHelper::select()
     */
    function generateList($data, $options = array()) {
        $defaults = array(
            'keyPath' => '{n}.{s}.id',
            'valuePath' => '{n}.{s}.name',
            'spacer' => '&nbsp;&nbsp;&nbsp;&nbsp;'
        );
        
        $options = array_merge($defaults, $options);
        
        $list = array();
        $this->_generateListRecursive($data, $list, 0, $options);
        
        return $list;
    }
    
    /**
     * Recursively generate list options
     * 
     * @param array $data Tree data
     * @param array &$list Reference to list array
     * @param int $depth Current depth level
     * @param array $options Options
     */
    function _generateListRecursive($data, &$list, $depth, $options) {
        foreach ($data as $item) {
            $modelName = key($item);
            $spacer = str_repeat($options['spacer'], $depth);
            
            if (isset($item[$modelName]['id']) && isset($item[$modelName]['name'])) {
                $list[$item[$modelName]['id']] = $spacer . $item[$modelName]['name'];
            }
            
            if (isset($item['children']) && !empty($item['children'])) {
                $this->_generateListRecursive($item['children'], $list, $depth + 1, $options);
            }
        }
    }
}

?>
