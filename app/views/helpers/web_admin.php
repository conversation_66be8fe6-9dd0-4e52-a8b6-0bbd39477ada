<?php

class WebAdminHelper extends Helper {

  var $helpers = array('Html', 'Form', 'Paginator', 'Session', 'Javascript');

  function formInput($fieldName, $options = array()) {

  	$view =& ClassRegistry::getObject('view');
  	if ($hiddenFields = $view->getVar('hiddenFields')) {
      if (in_array($fieldName, $hiddenFields)) {
        $options['type'] = 'hidden';
      }
  	}

  	$schema = $view->getVar('schema');

    $modelClass = $view->getVar('modelClass');

    $isTree = $view->getVar('isTree');

    $isAutoFillSummary = $view->getVar('isAutoFillSummary');

    $associations = $view->getVar('associations');

    $isKey = false;
    if ($isTree && $fieldName === 'parent_id') {
      $isKey = true;
    } elseif (!empty($associations['belongsTo'])) {
      foreach ($associations['belongsTo'] as $alias => $assocData) {
        if ($fieldName === $assocData['foreignKey']) {
          $isKey = true;
          if (isset($assocData['form']) && $assocData['form'] == 'tag_picker') {
            $options = array_merge(array('widget' => 'tag_picker'), $options);
          }
          break;
        }
      }
    }

    if (preg_match('/^[A-Z]/', $fieldName)) {
      if (isset($associations['hasAndBelongsToMany'][$fieldName]['form'])) {
        if ($associations['hasAndBelongsToMany'][$fieldName]['form'] == 'ordered') {
          return;
        } elseif ($associations['hasAndBelongsToMany'][$fieldName]['form'] == 'multiple_image_picker') {
          return;
        } elseif ($associations['hasAndBelongsToMany'][$fieldName]['form'] == 'tag_picker') {
          $options = array_merge(array('widget' => 'tag_picker'), $options);
        } elseif ($associations['hasAndBelongsToMany'][$fieldName]['form'] == 'multiple_checkbox') {
          $options = array_merge(array('multiple' => 'checkbox'), $options);
        }
      }
    }


    if ($fieldName == 'month' && $schema[$fieldName]['length'] == 2) {
      $options = array_merge(array('empty' => __('Please select', true), 'options' => $this->Form->__generateOptions('month', array('monthNames' => true))), $options);
    } elseif ($fieldName == 'year' && $schema[$fieldName]['length'] == 4) {
      $yearsOptions = null;
      if (isset($options['minYear'])) {
        $yearsOptions['min'] = $options['minYear'];
      }
      if (isset($options['maxYear'])) {
        $yearsOptions['max'] = $options['maxYear'];
      }
      $years = $this->Form->__generateOptions('year', $yearsOptions);
      $options = array_merge(array('empty' => __('Please select', true), 'options' => $years), $options);
    } elseif (isset($schema[$fieldName]) && $schema[$fieldName]['type'] == 'text' && !strstr($fieldName, 'meta_')) {
      $options = array_merge(array('widget' => 'editor'), $options);
    } elseif ($fieldName == 'slug') {
      $options = array_merge(array('widget' => 'slug'), $options);
    } elseif (substr($fieldName, -8) == 'image_id') {
      $options = array_merge(array('widget' => 'image_picker'), $options);
    } elseif ($isKey && $view->getVar(Inflector::variable(Inflector::pluralize(preg_replace('/_id$/', '', $fieldName))))) {
      $options = array_merge(array('empty' => __('Please select', true)), $options);
    }

    if ($isAutoFillSummary) {
      if ($summaryField = $view->getVar('summaryField')) {
        if ($summaryField == $fieldName) {
          $options['after'] = $this->tip('If you leave this blank, the first 130 characters of the first Content Block will be added to this field when you click the save button.');
        }
      }
    }

  	if (isset($options['widget'])) {
  	  switch ($options['widget']) {
  	    case 'tag_picker':
          if (isset($options['empty'])) {
            $options['empty'] = '';
          }
  	      if ($isKey) {
  	        $domId = $modelClass.Inflector::camelize($fieldName);
  	      } else {
  	        $domId = $fieldName.$fieldName;
  	      }
          $this->Javascript->codeBlock("new Webadmin.TagPicker('".$domId."');", array('inline' => false));
  	      break;
        case 'editor':
          $this->Javascript->codeBlock("new Editor('".$modelClass.Inflector::camelize($fieldName)."');", array('inline' => false));
          break;
        case 'slug':
          $fieldToSlugify = $view->getVar('displayField');
          if (isset($options['slugify'])) {
            $fieldToSlugify = $options['slugify'];
          }
          $this->Javascript->codeBlock("new UI.Slug('".$modelClass.Inflector::camelize($fieldToSlugify)."', '".$modelClass.Inflector::camelize($fieldName)."');", array('inline' => false));
          break;
        case 'image_picker':
          $alias = Inflector::camelize(preg_replace('/_id$/', '', $fieldName));
          $obj = isset($this->data[$alias]) ? $this->Javascript->object($this->data[$alias]) : $this->Javascript->object(array());
          $this->Javascript->codeBlock("new UI.ImagePicker('".$modelClass.Inflector::camelize($fieldName)."', {data : $obj});", array('inline' => false));
          break;
  	  }
  	  unset($options['widget']);
  	}

    return $this->Form->input($fieldName, $options);

  }

  function formCreate($model = null, $options = array()) {
    $options = array_merge(array('url' => $_SERVER['REQUEST_URI']), $options);
    return $this->Form->create($model, $options);
  }

  function formSubmit($caption = null, $options = array()) {
    return $this->Form->submit($caption, $options);
  }

  function formEnd($options = null) {
    return $this->Form->end($options);
  }

  function tip($tip) {

    return sprintf("<p class=\"help\">%s</p>", $tip);

  }

  function resultsPerPage() {

    $params = $this->Paginator->params();

    $options = array(10, 20, 50);

    $resultsPerPage = array();

    $view =& ClassRegistry::getObject('view');

    foreach ($options as $i => $option) {

      if ($option > $params['count']) {
        break;
      }

      if ($option == $params['options']['limit']) {
        $resultsPerPage[] = array(
          'url' => false,
          'text' => $option,
          'selected' => true,
        );
        continue;
      }

      $url = $view->passedArgs;

      if ($option == $params['defaults']['limit']) {
        unset($url['limit']);
      } else {
        $url = array_merge($url, array('limit' => $option));
      }

      if (isset($url['page'])) {
        unset($url['page']);
      }

      $resultsPerPage[] = array(
        'url' => $url,
        'text' => $option,
      );

    }

    if (empty($resultsPerPage)
     || (isset($view->passedArgs['limit'])
      && $view->passedArgs['limit'] == 'all')) {

      $resultsPerPage[] = array(
        'url' => false,
        'text' => __('All', true),
        'selected' => true,
      );

    } else {

      $url = array_merge($view->passedArgs, array('limit' => 'all'));

      if (isset($url['page'])) {
        unset($url['page']);
      }

      $resultsPerPage[] = array(
        'url' => $url,
        'text' => __('All', true),
      );

    }

    return $resultsPerPage;

  }

  function htmlLinkIfPermitted($title, $url = null, $htmlAttributes = array(), $confirmMessage = false, $escapeTitle = true, $returnTitleIfNotPermitted = true) {

    if (!$this->hasPermission($url)) {
      if ($returnTitleIfNotPermitted) {
        return $title;
      } else {
        return false;
      }
    }

    return $this->Html->link($title, $url, $htmlAttributes, $confirmMessage, $escapeTitle);
  }

  function sessionFlash() {

    if ($this->Session->check('Message.flash')) {
      $this->Session->flash();
    }

    if ($this->Session->check('Message.auth')) {
      $_SESSION['Message']['auth']['layout'] = 'webadmin_flash_bad';
      $this->Session->flash('auth');
    }

  }

  function relatedTabsLabels() {

    $view =& ClassRegistry::getObject('view');

    $associations = $view->getVar('associations');

    $pluralHumanName = $view->getVar('pluralHumanName');

    $singularHumanName = $view->getVar('singularHumanName');

    $out = '';

    foreach ($associations['belongsTo'] as $alias => $assocData) {

      if (!isset($assocData['tabInEditPage'])
      || !$assocData['tabInEditPage']) {
        continue;
      }

      $url = array(
        Configure::read('Routing.admin') => true,
        'controller' => $assocData['controllerPath'],
        'action' => 'edit',
        $this->data[$assocData['className']][$assocData['primaryKey']],
      );

      if (!$link = $this->htmlLinkIfPermitted($assocData['singularHumanName'], $url, array('class' => 'trigger'), false, true, false)) {
        continue;
      }

      $out .= '<li>'.$link.'</li>';

    }

    foreach ($associations['hasOne'] as $alias => $assocData) {

      if (!isset($assocData['tabInEditPage'])
      || !$assocData['tabInEditPage']) {
        continue;
      }

      $url = array(
        Configure::read('Routing.admin') => true,
        'controller' => $assocData['controllerPath'],
        'action' => 'edit',
        $this->data[$assocData['className']][$assocData['primaryKey']],
      );

      if (!$link = $this->htmlLinkIfPermitted($assocData['singularHumanName'], $url, array('class' => 'trigger'), false, true, false)) {
        continue;
      }

      $out .= '<li>'.$link.'</li>';

    }

    foreach ($associations['hasMany'] as $alias => $assocData) {

      if (!isset($assocData['manageInEditPage'])
      || !$assocData['manageInEditPage']) {
        continue;
      }

      $out .= '<li><a href="#'.$assocData['controllerPath'].'" class="trigger">'.Inflector::humanize($assocData['controllerPath']).'</a></li>';

    }

    foreach ($associations['hasAndBelongsToMany'] as $alias => $assocData) {

      if (!isset($assocData['form'])
      || !strstr($assocData['form'], 'ordered')) {
        continue;
      }

      $label = trim(str_replace($pluralHumanName, '', Inflector::humanize($assocData['withControllerPath'])));

      if (substr($label, -3) == ' On') {
        $label .= ' This ' . $singularHumanName;
      }

      $out .= '<li><a href="#'.$assocData['withControllerPath'].'" class="trigger">'.$label.'</a></li>';

    }

    if (strlen($out) == 0) {
      return false;
    }

    return $out;

  }

  function relatedTabsContents() {

    $view =& ClassRegistry::getObject('view');

    $associations = $view->getVar('associations');

    $modelClass = $view->getVar('modelClass');

    $primaryKey = $view->getVar('primaryKey');

    $out = '';

    foreach ($associations['hasOne'] as $alias => $assocData) {

      if (!isset($assocData['manageInEditPage'])
      || !$assocData['manageInEditPage']) {
        continue;
      }

      $url = '/webadmin/'.$assocData['controllerPath'].'/edit/'.$this->data['WebadminUserProfile']['id'];

//      pr($url);

      $out .= '<div class="tab">' . $this->requestAction($url, array('return'=>'return')) . '</div>';

    }

    foreach ($associations['hasMany'] as $alias => $assocData) {

      if (!isset($assocData['manageInEditPage'])
      || !$assocData['manageInEditPage']) {
        continue;
      }

      $url = $this->relatedContentUrl(
        $assocData['controllerPath'],
        $assocData['className'],
        $assocData['foreignKey'],
        $this->data[$modelClass][$primaryKey],
        $assocData['conditions']
      );

      $out .= '<div class="tab">' . $this->requestAction($url, array('return'=>'return')) . '</div>';

    }

    foreach ($associations['hasAndBelongsToMany'] as $alias => $assocData) {

      if (!isset($assocData['form'])
      || !strstr($assocData['form'], 'ordered')) {
        continue;
      }

      $url = $this->relatedContentUrl(
        $assocData['withControllerPath'],
        $assocData['withModel'],
        $assocData['foreignKey'],
        $this->data[$modelClass][$primaryKey],
        $assocData['conditions']
      );

      $out .= '<div class="tab">' . $this->requestAction($url, array('return'=>'return')) . '</div>';

    }

    if (strlen($out) == 0) {
      return false;
    }

    return $out;

  }

  function relatedContentUrl($controllerPath, $className, $foreignKey, $id, $conditions) {

    $url = '/webadmin/'.$controllerPath.'/index/';

    $url .= 'smf0:'.$className.'.'.$foreignKey.'/so0:equals/sv0:'.$id;

    $filterIndex = 1;

    if (is_array($conditions)) {

      foreach ($conditions as $modelField => $value) {

        $url .= '/smf'.$filterIndex.':'.$modelField.'/so'.$filterIndex.':equals/sv'.$filterIndex.':'.$value;

        $filterIndex++;

      }

    } elseif (is_string($conditions) && !empty($conditions)) {

      trigger_error(__('You must quote conditions as an array in your habtm assocation for this to work', true), E_USER_ERROR);

      return false;

    }

    return $url;

  }

  function hasPermission($url) {

    if (!$this->Session->check('Auth.Permissions')) {
      return true;
    }

    if (!is_array($url)) {
      return false;
    }

    extract($url);

    if (!isset($controller)) {
      $controller = $this->params['controller'];
    }

    $controller = Inflector::camelize($controller);

    if (!isset($action)) {
      $action = $this->params['action'];
    }

    $_admin = Configure::read('Routing.admin');

    if (((isset(${$_admin}) && ${$_admin}) || @$this->params['action'][$_admin]) && strpos($action, $_admin) === false) {
      $action = $_admin.'_'.$action;
    }

    $permission = 'controllers/'.$controller.'/'.$action;

    return in_array($permission, $this->Session->read('Auth.Permissions'));

  }


  function menu($items, $options = null) {

    $itemCount = count($items);

    if (!$itemCount) {
      return;
    }

    $out = '<ul>';

    $itemCounter = 1;

    foreach ($items as $item) {
      if (isset($item['url'])
      && !$this->hasPermission($item['url'])) {
        continue;
      }
      $liClasses = array();
      if ((isset($item['selected']) && $item['selected'])
      || (isset($item['url']['controller']) && $this->params['controller'] == $item['url']['controller'])) {
        $liClasses[] = 'selected';
      }
      if (isset($item['has_children']) && $item['has_children']) {
        $liClasses[] = 'has_children';
      }
      if ($itemCounter == 1) {
        $liClasses[] = 'first_child';
      }
      if ($itemCounter == $itemCount) {
        $liClasses[] = 'last_child';
      }
      $out .= '<li';
      if (!empty($liClasses)) {
        $out .= ' class="'.implode(' ', $liClasses).'"';
      }
      if (isset($options['id']) && $options['id'] == true) {
        $out .= ' id="';
        if (isset($options['id_prefix'])) {
          $out .= $options['id_prefix'];
        }
        $out .= $item['id'];
        $out .= '"';
      }
      $out .= '>';
      if (!isset($item['url'])
      || $item['url'] == false) {
        $out .= '<span>'.$item['text'].'</span>';
      } else {
        if (is_array($item['url']) && isset($options['url'])) {
          $item['url'] = array_merge($options['url'], $item['url']);
        }
        // Ensure webadmin menu links don't inherit plugin context
        if (is_array($item['url']) && !isset($item['url']['plugin'])) {
          $item['url']['plugin'] = null;
        }
        $out .= $this->Html->link($item['text'], $item['url'], null, null, false);
      }
      if (isset($item['children'])) {
        $out .= $this->menu($item['children'], $options);
      }
      $out .= '</li>';
      $itemCounter++;
    }

    $out .= "</ul>";

    return $out;
  }

}

?>
