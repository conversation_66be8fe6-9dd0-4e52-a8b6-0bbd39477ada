<?php
/**
 * Plugin Name: Bon Voyage Navigation Integration
 * Description: Integrates the navigation (megamenu and mmenu) from the main site into the WordPress blog
 * Version: 1.0
 * Author: Bon Voyage
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class BonVoyage_Navigation {
    /**
     * Constructor
     */
    public function __construct() {
        // Enqueue scripts and styles - use init hook to ensure it's not too early
        add_action('init', array($this, 'init'));

        // Add mobile menu HTML to the footer (will be moved to the header via JS)
        add_action('wp_footer', array($this, 'add_mobile_menu_html'));

        // Add mobile menu JavaScript to the footer
        add_action('wp_footer', array($this, 'add_mobile_menu_script'));

        // Add megamenu HTML to the footer
        add_action('wp_footer', array($this, 'add_megamenu_html'));

        // Add script to move the megamenu to the right place
        add_action('wp_footer', array($this, 'add_megamenu_placement_script'));

        // Modify the header template
        add_filter('timber/context', array($this, 'modify_header_template'));

        // Debug: Add a test action to verify the plugin is loaded
        if (isset($_GET['debug_scripts'])) {
            add_action('wp_footer', function() {
                echo '<!-- BV Menu: Plugin is loaded and initialized -->';
            }, 1);
        }
    }

    public function init() {
        // Hook into wp_enqueue_scripts with a lower priority
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'), 20);
    }

    /**
     * Add script to move the megamenu to the right place
     */
    public function add_megamenu_placement_script() {
        ?>
        <script>
            jQuery(document).ready(function($) {
                // Check if any megamenu element exists
                if ($('.mega-menu').length === 0) {
                    console.log('[Megamenu] No mega-menu found, creating placeholder');
                    $('.page-header__inner').append('<div id="megamenu-placeholder" class="mega-menu"></div>');
                } else {
                    console.log('[Megamenu] Existing mega-menu found, using it instead of creating a placeholder');
                    // If there's already a .mega-menu but no #megamenu-placeholder, add the ID to the first .mega-menu
                    if ($('#megamenu-placeholder').length === 0) {
                        $('.mega-menu').first().attr('id', 'megamenu-placeholder');
                    }
                    // Move existing mega-menu to page-header__inner if it's not already there
                    if (!$('.page-header__inner .mega-menu').length && $('.mega-menu').length) {
                        $('.mega-menu').appendTo('.page-header__inner');
                        console.log('[Megamenu] Moved existing mega-menu to page-header__inner');
                    }
                }

                // Add BV_NAV global object if it doesn't exist
                if (typeof window.BV_NAV === 'undefined') {
                    window.BV_NAV = {
                        endpoints: {
                            mobileMenu: '/mmenu',
                            megaMenu: '/megamenu'
                        }
                    };
                    console.log('[Navigation] Created BV_NAV global with endpoints:', window.BV_NAV.endpoints);
                }

                // Check if the megamenu content is already loaded
                if ($('#megamenu-placeholder').children().length === 0) {
                    console.log('[Megamenu] Megamenu placeholder is empty, content will be loaded from endpoint');
                }

                // Initialize MegaMenu if it exists in the global scope and hasn't been initialized yet
                $(window).on('load', function() {
                    setTimeout(function() {
                        // Check if already initialized by navigation-main.js
                        if (window.BV_NAV && window.BV_NAV.megaMenuInitialized) {
                            console.log('[Megamenu] MegaMenu already initialized by navigation-main.js, skipping initialization');
                            return;
                        }

                        // Check if already initialized by navigation.js
                        if (window.BV && window.BV.Navigation && window.BV.Navigation.megaMenuInitialized) {
                            console.log('[Megamenu] MegaMenu already initialized by navigation.js, skipping initialization');
                            return;
                        }

                        // If not initialized yet, try to initialize
                        if (window.MegaMenu && typeof window.MegaMenu.initialize === 'function') {
                            console.log('[Megamenu] Initializing MegaMenu from global scope');
                            window.MegaMenu.initialize();
                        } else if (window.BonVoyage && window.BonVoyage.MegaMenu) {
                            console.log('[Megamenu] Initializing MegaMenu from BonVoyage.MegaMenu');
                            window.BonVoyage.MegaMenu.initialize();
                        } else {
                            console.log('[Megamenu] MegaMenu not found in global scope, looking for it in other objects');
                            // Try to find MegaMenu in any common locations
                            var found = false;
                            for (var key in window) {
                                if (key.toLowerCase().includes('menu') && typeof window[key] === 'object' && window[key] !== null) {
                                    console.log('[Megamenu] Found potential menu object:', key);
                                    if (typeof window[key].initialize === 'function') {
                                        console.log('[Megamenu] Initializing from:', key);
                                        window[key].initialize();
                                        found = true;
                                        break;
                                    }
                                }
                            }

                            if (!found) {
                                console.log('[Megamenu] Could not find MegaMenu object to initialize');
                            }
                        }
                    }, 500); // Wait for everything to load
                });
            });
        </script>
        <?php
    }

    /**
     * Enqueue necessary scripts and styles
     */
    /**
     * Enqueue scripts and styles
     */
    public function enqueue_scripts() {
        // Only load on frontend
        if (is_admin()) {
            return;
        }

        // Get the plugin directory URL
        $plugin_url = plugin_dir_url(__FILE__);
        $main_site_url = 'https://' . $_SERVER['HTTP_HOST'];

        // We no longer load the main site's navigation.js script
        // This is to avoid duplicate script loading and conflicts
        if (defined('WP_DEBUG') && WP_DEBUG) {
            // error_log('Skipping main site navigation script to avoid duplication');
        }

        // Enqueue the navigation-main.js script specifically for the blog
        wp_enqueue_script(
            'bon-voyage-navigation-main',
            $plugin_url . 'js/navigation-main.js',
            array('jquery', 'mmenu-core-js'),
            @filemtime(plugin_dir_path(__FILE__) . 'js/navigation-main.js') ?: '1.0.0',
            true // Load in footer
        );

        // Add debug logging for our script
        $script_path = $plugin_url . 'js/navigation.js';
        $script_deps = array('jquery', 'bon-voyage-navigation-main');
        if (defined('WP_DEBUG') && WP_DEBUG) {
            // error_log('Loading blog navigation script from: ' . $script_path);
            // error_log('Script dependencies: ' . print_r($script_deps, true));
        }

        // Enqueue our navigation script (depends on the main navigation script)
        wp_enqueue_script(
            'bon-voyage-navigation',
            $script_path,
            $script_deps,
            @filemtime(plugin_dir_path(__FILE__) . 'js/navigation.js') ?: '1.0.0',
            true // Load in footer
        );

        // Localize the script with the site URL
        wp_localize_script('bon-voyage-navigation', 'bonVoyageNav', array(
            'mainSiteUrl' => $main_site_url,
            'isDebug' => (defined('WP_DEBUG') && WP_DEBUG) || isset($_GET['debug'])
        ));

        // Debug script is disabled to reduce console logging
        // Only enable when explicitly requested with debug_menu parameter
        if (isset($_GET['debug_menu'])) {
            $debug_script = '
                (function() {
                    function checkMegaMenu() {
                        console.log("=== MegaMenu Debug ===");
                        console.log("Main navigation script loaded");
                        console.log("Window.MegaMenu:", typeof window.MegaMenu !== "undefined" ? "Found" : "Not found");
                        console.log("Window.BonVoyage.MegaMenu:", (window.BonVoyage && typeof window.BonVoyage.MegaMenu !== "undefined") ? "Found" : "Not found");

                        // Try to find MegaMenu in any common locations
                        var found = false;

                        if (typeof window.MegaMenu !== "undefined") {
                            console.log("MegaMenu found at: window.MegaMenu");
                            found = true;
                        }

                        if (window.BonVoyage && typeof window.BonVoyage.MegaMenu !== "undefined") {
                            console.log("MegaMenu found at: window.BonVoyage.MegaMenu");
                            found = true;
                        }

                        if (!found) {
                            console.log("MegaMenu not found in common locations. Available globals:",
                                Object.keys(window).filter(function(key) {
                                    return key.toLowerCase().includes("menu") ||
                                           key.toLowerCase().includes("mega") ||
                                           key === "jQuery" ||
                                           key === "$";
                                })
                            );
                        }

                        // Check for megamenu elements
                        console.log("=== Megamenu Elements ===");
                        console.log("Megamenu placeholder exists:", document.getElementById("megamenu-placeholder") ? "Yes" : "No");
                        console.log("Mega-menu class elements:", document.querySelectorAll(".mega-menu").length);
                        console.log("Mega-menu panels:", document.querySelectorAll(".mega-menu__panel").length);

                        // Check for data-dropdown triggers
                        var triggers = document.querySelectorAll("[data-dropdown]");
                        console.log("Data-dropdown triggers:", triggers.length);
                        if (triggers.length > 0) {
                            console.log("Trigger examples:", Array.from(triggers).slice(0, 3).map(function(t) {
                                return {
                                    text: t.textContent.trim(),
                                    dropdown: t.getAttribute("data-dropdown")
                                };
                            }));
                        }

                        // Check if endpoints are defined
                        console.log("=== Endpoints ===");
                        if (window.BV_NAV && window.BV_NAV.endpoints) {
                            console.log("Mmenu endpoint:", window.BV_NAV.endpoints.mobileMenu);
                            console.log("Megamenu endpoint:", window.BV_NAV.endpoints.megaMenu);
                        } else {
                            console.log("BV_NAV endpoints not found");
                        }
                    }

                    // Only check once when explicitly requested
                    checkMegaMenu();

                    // Add a listener to check after everything is loaded
                    window.addEventListener("load", function() {
                        setTimeout(function() {
                            console.log("=== After Load MegaMenu Check ===");
                            checkMegaMenu();
                        }, 1000);
                    });
                })();
            ';

            // Add the debug script after our main navigation script
            wp_add_inline_script('bon-voyage-navigation-main', $debug_script, 'after');
        }
        // Enqueue the shared navigation CSS from the main site
        // Use the absolute URL to ensure it's loaded correctly
        // We need to bypass WordPress URL handling to get to the parent directory
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
        $host = $_SERVER['HTTP_HOST'];
        $main_site_url = $protocol . $host;

        // Check if the style is already registered
        global $wp_styles;
        $is_registered = isset($wp_styles->registered['navigation-css']);

        if (!$is_registered) {
            // Register the style with an absolute URL
            wp_register_style(
                'navigation-css',
                $main_site_url . '/css/navigation.css',
                array(),
                '1.0'
            );

            // Manually modify the registered style's src to ensure it doesn't get the WordPress base URL prepended
            if (isset($wp_styles->registered['navigation-css'])) {
                $wp_styles->registered['navigation-css']->src = $main_site_url . '/css/navigation.css';
            }

            // Log for debugging
            if (defined('WP_DEBUG') && WP_DEBUG) {
                // error_log('Registered navigation-css with URL: ' . $main_site_url . '/css/navigation.css');
            }
        } else {
            // Log for debugging
            if (defined('WP_DEBUG') && WP_DEBUG) {
                // error_log('navigation-css already registered, skipping registration');
            }
        }

        // Enqueue the style
        wp_enqueue_style('navigation-css');

        // Add debug script to check if CSS is loaded
        if (isset($_GET['debug_css'])) {
            add_action('wp_footer', function() use ($main_site_url) {
                echo "
                <script>
                    document.addEventListener('DOMContentLoaded', function() {
                        // Check if navigation.css is loaded
                        var isLoaded = false;
                        var styleSheets = document.styleSheets;
                        for (var i = 0; i < styleSheets.length; i++) {
                            try {
                                if (styleSheets[i].href && styleSheets[i].href.includes('/css/navigation.css')) {
                                    console.log('FOUND navigation.css: ' + styleSheets[i].href);
                                    isLoaded = true;
                                    break;
                                }
                            } catch (e) {
                                // Cross-origin stylesheet, skip it
                            }
                        }
                        console.log('navigation.css loaded: ' + isLoaded);
                        console.log('URL used: " . $main_site_url . "/css/navigation.css');
                    });
                </script>
                ";
            });
        }

        // Enqueue jQuery
        wp_enqueue_script('jquery');

        // Enqueue mmenu JavaScript from CDN
        wp_enqueue_script(
            'mmenu-core-js',
            'https://cdnjs.cloudflare.com/ajax/libs/mmenu-js/9.3.0/mmenu.min.js',
            array('jquery'),
            '8.5.24',
            true
        );

        // Enqueue mmenu CSS from CDN
        wp_enqueue_style(
            'mmenu-core-css',
            'https://cdnjs.cloudflare.com/ajax/libs/mmenu-js/9.3.0/mmenu.min.css',
            array(),
            '8.5.24'
        );

        // We're not loading the main site's navigation.js to avoid double initialization
        // Instead, we're initializing mmenu directly in this plugin

        // Add custom CSS for the WordPress blog
        $custom_css = "
        /* WordPress blog specific navigation styles */

        /* Mobile menu trigger button */
        .mmenu-trigger {
            position: absolute;
            top: 12px;
            left: 8px;
            border: none;
            background: none;
            padding: 0;
            cursor: pointer;
            z-index: 1001;
            padding: 10px;
        }

        /* Hide cross icon by default */
        .cross-icon {
            display: none !important;
        }

        .hamburger-icon {
            display: block !important;
        }

        /* Show cross icon when menu is active */
        .mmenu-trigger.active .hamburger-icon,
        html.mm-wrapper_opened .mmenu-trigger .hamburger-icon {
            display: none !important;
        }

        .mmenu-trigger.active .cross-icon,
        html.mm-wrapper_opened .mmenu-trigger .cross-icon {
            display: block !important;
        }

        /* Fix for WordPress admin bar */
        .admin-bar .mm-menu {
            top: 32px;
        }

        @media screen and (max-width: 782px) {
            .admin-bar .mm-menu {
                top: 46px;
            }
        }

        /* Header layout and positioning */
        .page-header {
            position: relative;
            z-index: 1000;
            background: #a80000;
            box-shadow: 0 7px 2px rgba(0, 0, 0, 0.3);
            flex-direction: column;
        }

        .page-header__inner {
            display: flex;
            align-items: center;
            /* padding: 0; */
            width: 100%;
            max-width: 1280px;
            margin: 0 auto;
            position: relative;
        }

        /* Logo styling */
        .page-header__logo {
            background-image: url('/img/site/sprites/logos/bv-logo-white.svg');
            background-repeat: no-repeat;
            background-position: center;
            background-size: contain;
            width: 281px;
            height: 50px;
            text-indent: -9999px;
            display: block;
            margin: 20px 18px 20px 0;
            flex-shrink: 0;
        }

        /* Desktop menu container */
        .desktop-menu {
            display: flex;
            /* flex-direction: column; */
            margin-left: 15px;
            margin-right: 40px;
            flex-grow: 1;
            margin-top: 15px;
        }

        /* Primary navigation */
        .primary-nav {
            padding: 0 0 7px 5px;
            display: flex;
        }

        .primary-nav ul {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .primary-nav li {
            padding: 0 10px;
            margin: 0;
        }

        /* Secondary navigation */
        .secondary-nav {
            display: flex;
            margin: 0;
            /* margin-top: 10px; */
        }

        .secondary-nav ul {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .secondary-nav li {
            margin: 0;
            position: relative;
            padding: 0 10px;
        }

        .secondary-nav__dest {
            color: #fff;
            display: block;
            padding: 10px 5px;
            text-decoration: none;
            white-space: nowrap;
        }

        /* Search toggle button */
        .search-toggle {
            position: absolute;
            top: 23px;
            right: 19px;
            width: 25px;
            height: 25px;
            background: url('/img/site/icons/search.svg') no-repeat;
            background-position: 0 0 !important;
            border: none;
            cursor: pointer;
            padding: 0;
            font-size: 0;
            color: transparent;
        }

        /* Search box */
        .primary-search--page-header {
            /* position: absolute;
            top: 12px;
            right: 18px;
            width: 200px; */
        }

        .primary-search__input {
            position: relative;
        }

        .primary-search__input input[type=text] {
            border-radius: 2px;
            border: 0;
            height: 35px;
            width: 100%;
            padding: 12px;
        }

        .primary-search__input .button-wrap {
            position: absolute;
            right: 0;
            top: 0;
            bottom: 0;
            display: flex;
            width: 40px;
            align-items: center;
            justify-content: center;
        }



        /* Mobile menu trigger button */
        .mmenu-trigger {
            position: absolute;
            top: 12px;
            left: 8px;
            border: none;
            background: none;
            padding: 10px;
            cursor: pointer;
            z-index: 1001;
        }

        /* Hide cross icon by default */
        .cross-icon {
            display: none !important;
        }

        .hamburger-icon {
            display: block !important;
        }

        /* Show cross icon when menu is active */
        .mmenu-trigger.active .hamburger-icon,
        html.mm-wrapper_opened .mmenu-trigger .hamburger-icon {
            display: none !important;
        }

        .mmenu-trigger.active .cross-icon,
        html.mm-wrapper_opened .mmenu-trigger .cross-icon {
            display: block !important;
        }

        /* Fix for WordPress admin bar */
        .admin-bar .mm-menu {
            top: 32px;
        }

        @media screen and (max-width: 782px) {
            .admin-bar .mm-menu {
                top: 46px;
            }
        }

        /* Mega Menu Styles */
        .mega-menu {
            position: absolute;
            left: 0;
            right: 0;
            top: auto;
            bottom: 0;
            z-index: 998;
            width: 100%;
        }

        /* Position the megamenu placeholder */
        /* #megamenu-placeholder {
            position: relative;
            z-index: 997;
            width: 100%;
        } */

        .mega-menu__panel {
            display: none;
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: #fff;
            box-shadow: 0 7px 10px rgba(0,0,0,0.1);
            padding: 30px;
            animation: fadeIn 0.2s ease-in-out;
            border-top: none !important;
            z-index: 1000;
            margin-top: 0;
        }

        /* Override any !important display:none that might be affecting the panels */
        .mega-menu__panel.is-active {
            display: block !important;
        }

        /* Show panel when parent is hovered */
        .secondary-nav__dest:hover + .mega-menu__panel,
        .secondary-nav__dest:focus + .mega-menu__panel,
        .mega-menu__panel:hover {
            display: block !important;
        }

        /* Panel Display */
        .secondary-nav li:hover > .mega-menu__panel {
            display: block;
        }

        .mega-menu__inner {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            gap: 40px;
        }

        .mega-menu__section {
            flex: 1;
        }

        .mega-menu__section h3 {
            color: #565044;
            font-size: 16px;
            font-weight: 600;
            margin: 0 0 15px 12px;
            text-transform: uppercase;
        }

        .mega-menu__section ul {
            margin: 0;
            padding: 0;
            list-style: none;
        }

        .mega-menu__section li {
            /* margin-bottom: 8px; */
        }

        .mega-menu__section a {
            color: #565044;
            text-decoration: none;
            font-size: 14px;
            transition: color 0.2s;
        }

        .mega-menu__section a:hover {
            color: #a80000;
        }

        .mega-menu__list--columns {
            display: grid !important;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)) !important;
            grid-auto-flow: column !important;
            grid-template-rows: repeat(7, auto) !important;
            gap: 2px !important;
            width: 100% !important;
            max-width: 1200px !important;
            margin: 0 auto !important;
        }

        /* Specific styling for Far & Wide dropdown with only 3 items */
        #far-wide-dropdown .mega-menu__list--columns {
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)) !important;
            grid-auto-flow: column !important;
            grid-template-rows: repeat(7, auto) !important;
        }

        /* Mega Menu Positioning and Width Constraints */
        .mega-menu {
            position: absolute;
            left: 0;
            right: 0;
            top: 85px;
            z-index: 998;
        }

        .mega-menu__panel {
            display: none;
            position: absolute;
            left: 0;
            right: 0;
            background: #fff;
            box-shadow: 0 7px 10px rgba(0,0,0,0.1);
            padding: 30px;
            animation: fadeIn 0.2s ease-in-out;
            border-top: none !important;
        }

        .mega-menu__panel.is-active {
            display: block;
        }

        .mega-menu__inner {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            gap: 40px;
        }

        .mega-menu__section {
            flex: 1;
        }

        /* Active state for secondary nav items */
        .secondary-nav li.is-active {
            background-color: #fff;
        }

        .secondary-nav li.is-active .secondary-nav__dest {
            color: #9a1e13;
        }

        /* Secondary Navigation Hover and Active States */
        .secondary-nav li:hover > .secondary-nav__dest,
        .secondary-nav li:has(.mega-menu__panel:hover) > .secondary-nav__dest {
            color: #9a1e13;
            text-decoration: none;
        }

        .secondary-nav li:hover,
        .secondary-nav li:has(.mega-menu__panel:hover) {
            background-color: #fff;
        }

        /* Animation for mega menu */
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Responsive styles */
        @media (max-width: 1023px) {
            .mega-menu {
                display: none !important;
            }
            .primary-search__input .button-wrap button {
                background: url('/img/site/icons/chevron_right_light.svg') no-repeat;
                background-position: 0 0 !important;
                margin: 0;
                width: 18px;
                height: 18px;
        }

            .desktop-menu {
                display: none !important;
            }

            .secondary-nav {
                display: none !important;
            }

            .primary-nav {
                display: none !important;
            }

            .primary-search--page-header {
                display: none !important;
            }

            .page-header__logo {
                margin: 20px auto!important;
                width: 187px;
                height: 34px;
            }
        }

        /* Medium screens */
        @media (min-width: 768px) and (max-width: 1023px) {
            .page-header__inner {
                padding: 0 15px;
            }

            .desktop-menu {
                margin-left: 10px;
                margin-right: 20px;
            }

            .secondary-nav li {
                padding: 0 5px;
            }

            .secondary-nav__dest {
                padding: 10px 3px;
                font-size: 14px;
            }

            .primary-search--page-header {
                width: 150px;
            }
        }

        /* Large screens */
        @media (min-width: 1024px) {
            .page-header__inner {
                padding: 0 15px;
            }
        }
        ";

        wp_add_inline_style('navigation-css', $custom_css);
    }

    /**
     * Add mobile menu HTML to the page
     */
    public function add_mobile_menu_html() {
        // Use a static flag to ensure we only output the mobile menu HTML once
        static $mobile_menu_added = false;

        // If we've already added the mobile menu, don't add it again
        if ($mobile_menu_added) {
            return;
        }

        // Properly enqueue the script to load mobile menu content
        add_action('wp_enqueue_scripts', function() {
            // Register the script
            wp_register_script(
                'bon-voyage-menu-content-loader',
                false, // No file, we'll add the script inline
                array('jquery'), // Depend on jQuery
                '1.0.0',
                true // Load in footer
            );

            // Enqueue the script
            wp_enqueue_script('bon-voyage-menu-content-loader');

            // Add the inline script to load mobile menu content
            wp_add_inline_script('bon-voyage-menu-content-loader', '
                jQuery(document).ready(function($) {
                    // Load mobile menu content via XHR
                    const mobileMenu = $("#mobile-menu");
                    if (mobileMenu.length) {
                        console.log("[MMenu] Loading mobile menu content from /mmenu endpoint");
                        $.ajax({
                            url: "/mmenu",
                            method: "GET",
                            dataType: "html",
                            success: function(response) {
                                console.log("[MMenu] Mobile menu content loaded successfully");
                                // Replace the content of the mobile menu
                                mobileMenu.html(response);
                                // Initialize mmenu after content is loaded
                                if (typeof window.BV_NAV !== "undefined" && typeof window.BV_NAV.initializeMmenu === "function") {
                                    window.BV_NAV.initializeMmenu();
                                }
                            },
                            error: function(xhr, status, error) {
                                console.error("[MMenu] Failed to load mobile menu content:", error);
                            }
                        });
                    } else {
                        console.error("[MMenu] Mobile menu element not found in the DOM");
                    }
                });
            ');
        });

        // Set the flag to indicate that we've added the mobile menu
        $mobile_menu_added = true;
    }

    /**
     * Fetch the mobile menu HTML from the main site
     *
     * @return string The mobile menu HTML or empty string if fetch failed
     */
    private function fetch_mobile_menu_from_main_site() {
        // URL to the mobile menu API endpoint on the main site
        // Use the correct endpoint /mmenu instead of /api/navigation/mmenu
        $api_url = '/mmenu';

        // Try to fetch the menu HTML
        $response = wp_remote_get($api_url, array(
            'timeout' => 5, // Short timeout to avoid slowing down the page
        ));

        // Check if the request was successful
        if (is_wp_error($response) || wp_remote_retrieve_response_code($response) !== 200) {
            // error_log('Failed to fetch mobile menu from main site: ' . (is_wp_error($response) ? $response->get_error_message() : wp_remote_retrieve_response_code($response)));
            return '';
        }

        // Get the response body
        $menu_html = wp_remote_retrieve_body($response);

        // Check if the response is valid HTML
        if (empty($menu_html) || !strpos($menu_html, '<nav id="mobile-menu"')) {
            // error_log('Invalid mobile menu HTML received from main site');
            return '';
        }

        // Log the first 100 characters of the menu HTML for debugging
        // error_log('Mobile menu HTML received from main site (first 100 chars): ' . substr($menu_html, 0, 100));

        return $menu_html;
    }

    /**
     * Get fallback mobile menu HTML
     *
     * @return string The fallback mobile menu HTML
     */
    private function get_fallback_mobile_menu_html() {
        ob_start();
        ?>
        <!-- Empty mobile menu HTML (Fallback) - Content will be loaded via XHR -->
        <nav id="mobile-menu" role="navigation" aria-label="Mobile navigation">
            <!-- Content will be loaded from the main site via XHR -->
        </nav>
        <?php
        return ob_get_clean();
    }

    /**
     * Add JavaScript for the mobile menu
     */
    public function add_mobile_menu_script() {
        // Add custom styles for the mobile menu
        add_action('wp_head', function() {
            ?>
            <style>
            /* Additional mmenu styles to ensure title is visible */
            .mm-navbar__title {
                display: block !important;
                padding: 0 !important;
                margin: 0 !important;
                text-overflow: ellipsis !important;
                overflow: hidden !important;
                white-space: nowrap !important;
                font-weight: bold !important;
                color: #4b4742 !important;
            }

            /* Ensure the logo is visible */
            .mm-logo {
                display: block !important;
                text-align: center !important;
                padding: 10px 0 !important;
            }

            .mm-logo img {
                max-height: 50px !important;
                width: auto !important;
            }
            </style>
            <?php
        });

        // Properly enqueue the mobile menu script
        add_action('wp_enqueue_scripts', function() {
            // Register the script
            wp_register_script(
                'bon-voyage-mobile-menu-setup',
                false, // No file, we'll add the script inline
                array('jquery'), // Depend on jQuery
                '1.0.0',
                true // Load in footer
            );

            // Enqueue the script
            wp_enqueue_script('bon-voyage-mobile-menu-setup');

            // Add the inline script
            wp_add_inline_script('bon-voyage-mobile-menu-setup', '
                jQuery(document).ready(function($) {
                    // Check if multiple mobile menu elements exist and remove duplicates
                    if ($("#mobile-menu").length > 1) {
                        console.log("[MMenu] Multiple mobile menu elements found, keeping only the first one");
                        $("#mobile-menu").not(":first").remove();
                    }

                    // Check if the mmenu trigger exists
                    if ($(".mmenu-trigger").length === 0) {
                        console.log("[MMenu] Menu trigger not found, creating it");
                        $(".page-header__inner").prepend(\'<button class="nav-toggle mmenu-trigger" aria-label="Toggle mobile menu" aria-expanded="false"><img src="/img/site/icons/hamburger.svg" alt="Menu" class="hamburger-icon"><img src="/img/site/icons/cross.svg" alt="Close" class="cross-icon" style="display: none;"></button>\');
                    }

                    // The click handler for the mmenu trigger is now managed by navigation-main.js
                    // This prevents duplicate event handlers that could conflict with each other

                    // Add a small debugging script to help identify any remaining issues
                    console.log("[MMenu] Checking mmenu trigger setup:");
                    console.log("[MMenu] Trigger element exists:", $(".mmenu-trigger").length > 0);
                    console.log("[MMenu] Hamburger icon exists:", $(".mmenu-trigger .hamburger-icon").length > 0);
                    console.log("[MMenu] Cross icon exists:", $(".mmenu-trigger .cross-icon").length > 0);
                });
            ');
        });
    }

    /**
     * Add megamenu HTML to the footer
     */
    public function add_megamenu_html() {
        // Add the megamenu HTML to the footer
        add_action('wp_footer', function() {
            ob_start();
            ?>
            <!-- NAVIGATION SOURCE: bon-voyage-navigation.php -->
            <div class="mega-menu">
                <!-- USA Dropdown -->
                <div class="mega-menu__panel" id="usa-dropdown">
                    <div class="mega-menu__inner">
                        <div class="mega-menu__section">
                            <h3>Popular Destinations</h3>
                            <ul class="mega-menu__list mega-menu__list--columns">
                                <li class="mega-menu__item">
                                    <a href="/destinations/new_york" class="mega-menu__link" title="New York">New York</a>
                                </li>
                                <li class="mega-menu__item">
                                    <a href="/destinations/florida" class="mega-menu__link" title="Florida">Florida</a>
                                </li>
                                <li class="mega-menu__item">
                                    <a href="/destinations/california" class="mega-menu__link" title="California">California</a>
                                </li>
                                <li class="mega-menu__item">
                                    <a href="/destinations/las_vegas" class="mega-menu__link" title="Las Vegas">Las Vegas</a>
                                </li>
                                <li class="mega-menu__item">
                                    <a href="/destinations/hawaii" class="mega-menu__link" title="Hawaii">Hawaii</a>
                                </li>
                                <li class="mega-menu__item">
                                    <a href="/destinations/washington_dc" class="mega-menu__link" title="Washington DC">Washington DC</a>
                                </li>
                                <li class="mega-menu__item">
                                    <a href="/destinations/chicago" class="mega-menu__link" title="Chicago">Chicago</a>
                                </li>
                                <li class="mega-menu__item">
                                    <a href="/destinations/boston" class="mega-menu__link" title="Boston">Boston</a>
                                </li>
                            </ul>
                        </div>
                        <div class="mega-menu__section">
                            <h3>Regions</h3>
                            <ul class="mega-menu__list">
                                <li class="mega-menu__item">
                                    <a href="/destinations/new_england" class="mega-menu__link" title="New England">New England</a>
                                </li>
                                <li class="mega-menu__item">
                                    <a href="/destinations/the_deep_south" class="mega-menu__link" title="The Deep South">The Deep South</a>
                                </li>
                                <li class="mega-menu__item">
                                    <a href="/destinations/texas" class="mega-menu__link" title="Texas">Texas</a>
                                </li>
                                <li class="mega-menu__item">
                                    <a href="/destinations/alaska" class="mega-menu__link" title="Alaska">Alaska</a>
                                </li>
                                <li class="mega-menu__item">
                                    <a href="/destinations/rocky_mountains" class="mega-menu__link" title="Rocky Mountains">Rocky Mountains</a>
                                </li>
                                <li class="mega-menu__item">
                                    <a href="/destinations/pacific_northwest" class="mega-menu__link" title="Pacific Northwest">Pacific Northwest</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Canada Dropdown -->
                <div class="mega-menu__panel" id="canada-dropdown">
                    <div class="mega-menu__inner">
                        <div class="mega-menu__section">
                            <h3>Popular Destinations</h3>
                            <ul class="mega-menu__list mega-menu__list--columns">
                                <li class="mega-menu__item">
                                    <a href="/destinations/toronto" class="mega-menu__link" title="Toronto">Toronto</a>
                                </li>
                                <li class="mega-menu__item">
                                    <a href="/destinations/vancouver" class="mega-menu__link" title="Vancouver">Vancouver</a>
                                </li>
                                <li class="mega-menu__item">
                                    <a href="/destinations/montreal" class="mega-menu__link" title="Montreal">Montreal</a>
                                </li>
                                <li class="mega-menu__item">
                                    <a href="/destinations/canadian_rockies" class="mega-menu__link" title="Canadian Rockies">Canadian Rockies</a>
                                </li>
                                <li class="mega-menu__item">
                                    <a href="/destinations/niagara_falls" class="mega-menu__link" title="Niagara Falls">Niagara Falls</a>
                                </li>
                                <li class="mega-menu__item">
                                    <a href="/destinations/banff" class="mega-menu__link" title="Banff">Banff</a>
                                </li>
                            </ul>
                        </div>
                        <div class="mega-menu__section">
                            <h3>Regions</h3>
                            <ul class="mega-menu__list">
                                <li class="mega-menu__item">
                                    <a href="/destinations/quebec" class="mega-menu__link" title="Quebec">Quebec</a>
                                </li>
                                <li class="mega-menu__item">
                                    <a href="/destinations/calgary" class="mega-menu__link" title="Calgary">Calgary</a>
                                </li>
                                <li class="mega-menu__item">
                                    <a href="/destinations/ottawa" class="mega-menu__link" title="Ottawa">Ottawa</a>
                                </li>
                                <li class="mega-menu__item">
                                    <a href="/destinations/british_columbia" class="mega-menu__link" title="British Columbia">British Columbia</a>
                                </li>
                                <li class="mega-menu__item">
                                    <a href="/destinations/alberta" class="mega-menu__link" title="Alberta">Alberta</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Far & Wide Dropdown -->
                <div class="mega-menu__panel" id="far-wide-dropdown">
                    <div class="mega-menu__inner">
                        <div class="mega-menu__section">
                            <h3>Far & Wide</h3>
                            <ul class="mega-menu__list mega-menu__list--columns">
                                <li class="mega-menu__item">
                                    <a href="/far_wide/south_america" class="mega-menu__link" title="South America">
                                        South America
                                    </a>
                                </li>
                                <li class="mega-menu__item">
                                    <a href="/far_wide/worldwide" class="mega-menu__link" title="Worldwide">
                                        Worldwide
                                    </a>
                                    <ul class="mega-menu__sublist">
                                        <li class="mega-menu__subitem">
                                            <a href="/far_wide/worldwide/cruises" class="mega-menu__sublink" title="Cruises">
                                                Cruises
                                            </a>
                                        </li>
                                        <li class="mega-menu__subitem">
                                            <a href="/far_wide/worldwide/escorted_tours" class="mega-menu__sublink" title="Escorted Tours">
                                                Escorted Tours
                                            </a>
                                        </li>
                                    </ul>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Holiday Types Dropdown -->
                <div class="mega-menu__panel" id="holiday-types-dropdown">
                    <div class="mega-menu__inner">
                        <div class="mega-menu__section">
                            <h3>Holiday Types</h3>
                            <ul class="mega-menu__list mega-menu__list--columns">
                                <li class="mega-menu__item">
                                    <a href="/holidays/multi_centre_combination_holidays" class="mega-menu__link" title="Multi-Centre Holidays">Multi-Centre Holidays</a>
                                </li>
                                <li class="mega-menu__item">
                                    <a href="/holidays/fly_drive_holidays" class="mega-menu__link" title="Fly-Drive Holidays">Fly-Drive Holidays</a>
                                </li>
                                <li class="mega-menu__item">
                                    <a href="/holidays/route_66_holidays" class="mega-menu__link" title="Route 66 Holidays">Route 66 Holidays</a>
                                </li>
                                <li class="mega-menu__item">
                                    <a href="/holidays/cruise_holidays" class="mega-menu__link" title="Cruise Holidays">Cruise Holidays</a>
                                </li>
                                <li class="mega-menu__item">
                                    <a href="/holidays/rail_holidays" class="mega-menu__link" title="Rail Holidays">Rail Holidays</a>
                                </li>
                                <li class="mega-menu__item">
                                    <a href="/holidays/ranch_holidays" class="mega-menu__link" title="Ranch Holidays">Ranch Holidays</a>
                                </li>
                                <li class="mega-menu__item">
                                    <a href="/holidays/motorhome_holidays" class="mega-menu__link" title="Motorhome Holidays">Motorhome Holidays</a>
                                </li>
                                <li class="mega-menu__item">
                                    <a href="/holidays/escorted_tours" class="mega-menu__link" title="Escorted Tours">Escorted Tours</a>
                                </li>
                            </ul>
                        </div>
                        <div class="mega-menu__section">
                            <h3>Special Interest</h3>
                            <ul class="mega-menu__list">
                                <li class="mega-menu__item">
                                    <a href="/holidays/winter_holidays" class="mega-menu__link" title="Winter Holidays">Winter Holidays</a>
                                </li>
                                <li class="mega-menu__item">
                                    <a href="/holidays/family_holidays" class="mega-menu__link" title="Family Holidays">Family Holidays</a>
                                </li>
                                <li class="mega-menu__item">
                                    <a href="/holidays/luxury_holidays" class="mega-menu__link" title="Luxury Holidays">Luxury Holidays</a>
                                </li>
                                <li class="mega-menu__item">
                                    <a href="/holidays/city_breaks" class="mega-menu__link" title="City Breaks">City Breaks</a>
                                </li>
                                <li class="mega-menu__item">
                                    <a href="/holidays/beach_holidays" class="mega-menu__link" title="Beach Holidays">Beach Holidays</a>
                                </li>
                                <li class="mega-menu__item">
                                    <a href="/holidays/adventure_holidays" class="mega-menu__link" title="Adventure Holidays">Adventure Holidays</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- What's Hot Dropdown -->
                <div class="mega-menu__panel" id="whats-hot-dropdown">
                    <div class="mega-menu__inner">
                        <div class="mega-menu__section">
                            <h3>What's Hot</h3>
                            <ul class="mega-menu__list mega-menu__list--columns">
                                <li class="mega-menu__item">
                                    <a href="/spotlights/special_offers" class="mega-menu__link" title="Special Offers">Special Offers</a>
                                </li>
                                <li class="mega-menu__item">
                                    <a href="/spotlights/new_destinations" class="mega-menu__link" title="New Destinations">New Destinations</a>
                                </li>
                                <li class="mega-menu__item">
                                    <a href="/spotlights/seasonal_highlights" class="mega-menu__link" title="Seasonal Highlights">Seasonal Highlights</a>
                                </li>
                                <li class="mega-menu__item">
                                    <a href="/spotlights/featured_holidays" class="mega-menu__link" title="Featured Holidays">Featured Holidays</a>
                                </li>
                                <li class="mega-menu__item">
                                    <a href="/spotlights/early_booking_offers" class="mega-menu__link" title="Early Booking Offers">Early Booking Offers</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- About Us Dropdown -->
                <div class="mega-menu__panel" id="about-dropdown">
                    <div class="mega-menu__inner">
                        <div class="mega-menu__section">
                            <h3>About Bon Voyage</h3>
                            <ul class="mega-menu__list mega-menu__list--columns">
                                <li class="mega-menu__item">
                                    <a href="/page/our_customers_say" class="mega-menu__link" title="Our Customers Say">Our Customers Say</a>
                                </li>
                                <li class="mega-menu__item">
                                    <a href="/page/fully_bonded_for_your_protection" class="mega-menu__link" title="Fully Bonded for Your Protection">Fully Bonded for Your Protection</a>
                                </li>
                                <li class="mega-menu__item">
                                    <a href="/page/telephone_numbers" class="mega-menu__link" title="Telephone Numbers">Telephone Numbers</a>
                                </li>
                                <li class="mega-menu__item">
                                    <a href="/page/address_and_registered_details" class="mega-menu__link" title="Address and Registered Details">Address and Registered Details</a>
                                </li>
                                <li class="mega-menu__item">
                                    <a href="/page/bon_voyage_feefo_rating" class="mega-menu__link" title="Bon Voyage Feefo Rating">Bon Voyage Feefo Rating</a>
                                </li>
                                <li class="mega-menu__item">
                                    <a href="/page/finding_us" class="mega-menu__link" title="Finding Us">Finding Us</a>
                                </li>
                                <li class="mega-menu__item">
                                    <a href="/page/careers" class="mega-menu__link" title="Careers">Careers</a>
                                </li>
                                <li class="mega-menu__item">
                                    <a href="/page/press_centre" class="mega-menu__link" title="Press Centre">Press Centre</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <?php
            $megamenu_html = ob_get_clean();
            echo "<!-- Megamenu HTML -->\n";
            echo $megamenu_html;
        }, 998); // Use a high priority to ensure it runs after other footer content but before the mobile menu
    }

    /**
     * Modify the header template
     */
    public function modify_header_template($context) {
        // Add flags to indicate that we're using the shared navigation
        $context['use_shared_navigation'] = true;

        // Add a flag to indicate that we should use the white logo
        $context['use_white_logo'] = true;

        return $context;
    }
}

// Initialize the plugin
new BonVoyage_Navigation();
