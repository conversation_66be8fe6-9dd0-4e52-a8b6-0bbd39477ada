/* Custom CSS for Bon Voyage Blog */

/* Header layout */
.page-header {
    display: flex;
    position: relative;
    z-index: 1000;
    background: #a80000;
    box-shadow: 0 7px 2px rgba(0, 0, 0, 0.3);
    color: #fff;
}

.page-header a {
    color: #fff;
}

.page-header__inner {
    display: flex;
    align-items: center;
    /* padding: 0; */
    align-self: stretch;
    width: 100%;
    position: relative;
}

/* Logo styling */
.page-header__logo {
    display: flex;
    align-items: center;
    margin: 20px 18px 20px 0;
    flex-shrink: 0;
    position: static;
}

@media screen and (min-width: 1024px) {
    .page-header__logo {
        width: 281px;
        height: 50px;
    }
}

.page-header__logo img {
    max-width: 100%;
    height: auto;
    display: block;
}

/* Responsive adjustments for the header */
@media (max-width: 767px) {
    .page-header__inner {
        flex-direction: row;
        padding: 0 15px;
    }
}

@media (min-width: 768px) and (max-width: 1023px) {
    .page-header__inner {
        padding: 0 15px;
    }
}

@media (min-width: 1024px) {
    .page-header__inner {
        padding: 0 20px;
    }
}

/* Navigation styling */
.secondary-nav__dest {
    display: block;
    padding: 10px 5px;
    white-space: nowrap;
}

/* Hamburger menu button styling */
.mmenu-trigger {
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 12px;
    left: 8px;
    z-index: 1001;
}

.mmenu-trigger img {
    width: 30px;
    height: 30px;
    transition: all 0.3s ease;
}

.hamburger-icon,
.cross-icon {
    display: block;
}

/* Mobile menu styling */
#mobile-menu {
    max-width: 80vw;
    max-height: 100vh;
}

.mm-panels {
    overflow-y: auto !important;
    overflow-x: hidden !important;
}

/* mmenu specific styles */
.mm-menu {
    --mm-color-background: #a80000;
    --mm-color-text: #fff;
    --mm-color-button: #fff;
    --mm-color-border: rgba(255,255,255,0.3);
}

.mm-listitem {
    color: #fff;
    border-color: var(--mm-color-border);
}

.mm-navbar {
    border-color: var(--mm-color-border);
}

/* Hide hamburger on desktop */
@media (min-width: 768px) {
    .mmenu-trigger {
        display: none;
    }

    /* Desktop menu styling */
    .desktop-menu {
        display: flex;
        align-self: stretch;
        align-items: flex-start;
        flex-grow: 1;
        flex-shrink: 0;
        margin-left: 15px;
        margin-right: 40px;
        margin-top: 15px;
    }

    .desktop-menu__inner {
        display: flex;
        flex-direction: column;
    }

    .primary-nav {
        padding: 0 0 7px 5px;
        display: flex;
    }

    .primary-nav ul {
        display: flex;
        list-style: none;
        margin: 0;
        padding: 0;
    }

    .primary-nav li {
        padding: 0 10px;
        margin: 0;
    }
}

/* Secondary navigation styling */
.secondary-nav {
    display: flex;
    position: relative;
}

.secondary-nav ul {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}

.secondary-nav li {
    margin: 0;
    position: relative;
    padding: 0 10px;
}

.secondary-nav__dest {
    color: #fff;
    display: block;
    padding: 10px 5px;
    text-decoration: none;
    white-space: nowrap;
}

.secondary-nav__dest svg {
    margin-left: 7px;
    vertical-align: middle;
}

@media (min-width: 768px) {
    .secondary-nav > ul > li {
        padding-bottom: 10px;
    }
}

/* Ensure Far & Wide navigation item has consistent hover states */
.secondary-nav li:hover > .secondary-nav__dest,
.secondary-nav li:has(.mega-menu__panel:hover) > .secondary-nav__dest {
    color: #9a1e13 !important;
    text-decoration: none;
}

.secondary-nav li:hover,
.secondary-nav li:has(.mega-menu__panel:hover),
.secondary-nav li.is-active {
    background-color: #fff !important;
}

/* Ensure consistent bottom border/line styling */
@media (min-width: 768px) {
    .secondary-nav > ul > li {
        border-bottom: 1px solid rgba(255, 255, 255, 0.3);
        transition: all 0.25s ease;
    }

    .secondary-nav > ul > li:hover {
        border-bottom-color: #9a1e13;
    }
}

.secondary-nav__dest svg {
    position: relative;
    top: -1px;
}

@media screen and (min-width: 980px) {
    .secondary-nav li {
        max-width: 100% !important;
    }
}

@media screen and (max-width: 1024px) {
    .page-header__inner {
        padding: 0 20px;
    }
}

/* #mobile-menu {
    max-width: 80vw;
    max-height: 100vh;
} */

.page-content-body__content {
    padding: 0 18px;
}
.page-content-body {
    padding: 0;
}
.page-content-header {
    padding: 20px 18px;
}
