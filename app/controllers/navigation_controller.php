<?php

/**
 * Navigation Controller
 *
 * Provides API endpoints for navigation elements that can be consumed by the WordPress blog
 */
class NavigationController extends AppController {

    var $name = 'Navigation';
    var $uses = array('Destination', 'HolidayType', 'Spotlight', 'Page');
    var $components = array('Navigation', 'RequestHandler');
    var $helpers = array('App');

    /**
     * Before filter - allow API endpoints without authentication
     */
    function beforeFilter() {
        parent::beforeFilter();

        // Only modify Auth for this controller's actions - preserve existing allowed actions
        if (in_array($this->action, array('megamenu', 'mmenu'))) {
            // Get current allowed actions and add our actions
            $currentAllowed = (array)$this->Auth->allowedActions;
            $this->Auth->allowedActions = array_merge($currentAllowed, array('megamenu', 'mmenu'));

            // Set response type for API endpoints
            $this->RequestHandler->respondAs('html');
            $this->layout = 'ajax';
        }
    }

    /**
     * API endpoint to get the megamenu HTML
     * This can be called from the WordPress blog to get the latest megamenu content
     */
    function megamenu() {
        // Get navigation data
        $navigationData = $this->Navigation->getNavigationData();

        // Extract navigation components
        $mainNav = $navigationData['mainNav'];
        $usaDestinations = $navigationData['usaDestinations'];
        $canadaDestinations = $navigationData['canadaDestinations'];
        $holidayTypes = $navigationData['holidayTypes'];
        $whatsHot = $navigationData['whatsHot'];
        $holidayInfoPages = $navigationData['holidayInfoPages'];
        $aboutPages = $navigationData['aboutPages'];
        $farWideMenuItems = $navigationData['farWideMenuItems'];

        // Set variables for the view
        $this->set(compact('mainNav', 'usaDestinations', 'canadaDestinations', 'holidayTypes', 'whatsHot', 'holidayInfoPages', 'aboutPages', 'farWideMenuItems'));

        // Render the megamenu element
        $this->render('/elements/chrome/mega_menu_api');
    }

    /**
     * API endpoint to get the mmenu HTML
     * This can be called from the WordPress blog to get the latest mobile menu content
     */
    function mmenu() {
        // Use the shared mobile navigation builder
        $mobileNavigation = $this->Navigation->buildMobileNavigation();

        // Set variables for the view
        $this->set(compact('mobileNavigation'));

        // Render the mmenu element
        $this->render('/elements/chrome/mmenu_api');
    }

    /**
     * Generate Far & Wide navigation structure for sidebar
     * This creates a navigation array similar to regular page navigation
     * but based on the Far & Wide menu hierarchy
     */
    function getFarWideNavigation($currentUrl = null) {
        $this->autoRender = false;
        $this->layout = false;

        // Get all Far & Wide menu items
        $navigationMenu = ClassRegistry::init('NavigationMenu');
        $farWideItems = $navigationMenu->find('threaded', array(
            'conditions' => array(
                'NavigationMenu.menu_type' => 'far_wide',
                'NavigationMenu.published' => 1
            ),
            'fields' => array(
                'NavigationMenu.id',
                'NavigationMenu.parent_id',
                'NavigationMenu.name',
                'NavigationMenu.url'
            ),
            'order' => 'NavigationMenu.lft ASC',
            'recursive' => -1
        ));

        // Build navigation structure similar to regular page navigation
        $navigation = array();

        if (!empty($farWideItems)) {
            // Find the root "Far & Wide" item
            $rootItem = $farWideItems[0];

            $navigation[] = array(
                'text' => $rootItem['NavigationMenu']['name'],
                'url' => '#',
                'selected' => false,
                'has_children' => !empty($rootItem['children']),
                'children' => $this->_buildFarWideNavChildren($rootItem['children'], $currentUrl)
            );
        }

        return $navigation;
    }

    /**
     * Helper method to build Far & Wide navigation children
     */
    private function _buildFarWideNavChildren($items, $currentUrl) {
        $children = array();

        foreach ($items as $item) {
            $isSelected = ($item['NavigationMenu']['url'] === $currentUrl);

            $child = array(
                'text' => $item['NavigationMenu']['name'],
                'url' => $item['NavigationMenu']['url'],
                'selected' => $isSelected,
                'has_children' => !empty($item['children']),
                'children' => array()
            );

            // Add grandchildren if they exist
            if (!empty($item['children'])) {
                $child['children'] = $this->_buildFarWideNavChildren($item['children'], $currentUrl);
            }

            $children[] = $child;
        }

        return $children;
    }
}
