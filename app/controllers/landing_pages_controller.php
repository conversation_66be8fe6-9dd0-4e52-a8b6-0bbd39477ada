<?php

class LandingPagesController extends AppController {

  var $name = 'LandingPages';
  var $components = array('Section', 'Navigation');

  function index() {

    $this->criticalCss = 'campaigns';

    $this->paginate['LandingPage'] = array(
      'limit' => 10
    );

    $landingPages = $this->paginate('LandingPage');

    $this->_canonicalUrlForPaginated();

    $breadcrumbs = array(array(
      'text' => "Campaigns",
      'url'  => $this->here
    ));

    $heroBannerImage = RESOURCES_HOSTNAME . "/img/site/placeholders/spotlights.jpg";

    $this->set(compact('landingPages', 'breadcrumbs', 'heroBannerImage'));

  }

  function view() {

    // Check if this is a Far & Wide section request
    if (isset($this->params['section']) && $this->params['section'] == 'far_wide') {
      $this->_handleFarWideView();
      return;
    }

    $related = ClassRegistry::init('HolidayType')->getHolidayTypesByLandingPage($this->sectionId);

    $landingPageActivities = ClassRegistry::init('Activity')->getActivitiesByLandingPage($this->sectionId);

    $hideBreadcrumb = true;

    $this->set(compact('landingPageActivities', 'related', 'hideBreadcrumb'));

  }

  function _handleFarWideView() {
    $slug = $this->params['landing_page_slug'];
    $parentSlug = isset($this->params['parent_slug']) ? $this->params['parent_slug'] : null;

    // Build the URL based on whether it's nested or not
    if ($parentSlug) {
      $url = '/far_wide/' . $parentSlug . '/' . $slug;
    } else {
      $url = '/far_wide/' . $slug;
    }

    // Find the navigation menu item by URL
    $navigationMenu = ClassRegistry::init('NavigationMenu')->find('first', array(
      'conditions' => array(
        'NavigationMenu.url' => $url,
        'NavigationMenu.menu_type' => 'far_wide',
        'NavigationMenu.published' => 1
      ),
      'contain' => array('Page' => array('ContentBlock'))
    ));

    if (empty($navigationMenu)) {
      $this->cakeError('error404');
      return;
    }

    // If a page is associated, use its content
    if (!empty($navigationMenu['NavigationMenu']['page_id']) && !empty($navigationMenu['Page'])) {
      $page = $navigationMenu['Page'];

      // Get content blocks for the page
      $contentBlocks = ClassRegistry::init('ContentBlock')->find('all', array(
        'conditions' => array(
          'ContentBlock.model' => 'Page',
          'ContentBlock.modelid' => $page['id']
        ),
        'order' => 'ContentBlock.order ASC'
      ));

      // Set up Far & Wide breadcrumbs
      $breadcrumbs = array(
        array(
          'text' => 'Far & Wide',
          'url' => '/far_wide'
        )
      );

      // If this is a nested item, add the parent to breadcrumbs
      if ($parentSlug && !empty($navigationMenu['NavigationMenu']['parent_id'])) {
        $parentMenu = ClassRegistry::init('NavigationMenu')->findById($navigationMenu['NavigationMenu']['parent_id']);
        if (!empty($parentMenu)) {
          $breadcrumbs[] = array(
            'text' => $parentMenu['NavigationMenu']['name'],
            'url' => $parentMenu['NavigationMenu']['url']
          );
        }
      }

      $breadcrumbs[] = array(
        'text' => $navigationMenu['NavigationMenu']['name'],
        'url' => $this->here
      );

      $this->pageTitle = $page['meta_title'] ?: $navigationMenu['NavigationMenu']['name'];
      $this->set(compact('page', 'contentBlocks', 'navigationMenu', 'breadcrumbs'));
      $this->render('far_wide_page');
    } else {
      // No page associated, show 404 or redirect
      $this->cakeError('error404');
    }
  }

}

?>
