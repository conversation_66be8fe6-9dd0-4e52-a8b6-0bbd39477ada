<?php

class ReflectionBehavior extends ModelBehavior {

  var $_defaults = array(
    'tree_behavior_name' => 'Tree',
  );

  var $info;

  function setup(&$model, $config = array()) {

    if (!is_array($config)) {
      $config = array($config);
    }

    $this->settings[$model->alias] = array_merge($this->_defaults, $config);

  }

  function associatedLists(&$model) {

    extract($this->getInfo($model));

    $lists = array();

    foreach ($associations['belongsTo'] as $alias => $assocData) {

      if (isset($assocData['list']) && $assocData['list'] == false) {
        continue;
      }

      if (substr($assocData['foreignKey'], -8) == 'image_id') {
        continue;
      }

      $pluralVar = $assocData['pluralVar'];

      if ($assocData['isTree']
      && (!isset($assocData['list']) || $assocData['list'] == 'tree')) {
        $$pluralVar = $model->$alias->generatetreelist();
      } else {
        $$pluralVar = $model->$alias->find('list');
      }

      $lists[$pluralVar] = $$pluralVar;

    }

    foreach ($associations['hasAndBelongsToMany'] as $alias => $assocData) {

      if (isset($assocData['list']) && $assocData['list'] == false) {
        continue;
      }

      $pluralVar = $assocData['pluralVar'];

      if ($assocData['isTree']
      && (!isset($assocData['list']) || $assocData['list'] == 'isTree')) {
        $$pluralVar = $model->$alias->generatetreelist();
      } else {
        $$pluralVar = $model->$alias->find('list');
      }

      $lists[$pluralVar] = $$pluralVar;

    }

    if ($isTree) {
      $pluralVar = 'parents';
      $$pluralVar = $model->generatetreelist();
      $lists[$pluralVar] = $$pluralVar;
    }

    return $lists;

  }

  function enumLists(&$model) {

    $lists = array();

    if (isset($model->enums)
    && is_array($model->enums)
    && !empty($model->enums)) {

      foreach ($model->enums as $var => $options) {

        if (Set::numeric(array_keys($options))) {
          $options = array_combine($options, $options);
        }

        $$var = $options;

        $lists[$var] = $$var;
      }

    }

    return $lists;

  }

  function breadcrumbData(&$model, $parentId = null, $new = false) {

    $breadcrumbs = $model->getpath($parentId);

    if ($new) {
      $breadcrumbs[][$model->alias][$model->displayField] = 'New ' . Inflector::humanize(Inflector::underscore($model->alias));
    } else {
      if (is_array($breadcrumbs) && count($breadcrumbs) > 0) {
        unset($breadcrumbs[count($breadcrumbs)-1][$model->alias][$model->primaryKey]);
      }
    }

    $breadcrumbDisplayField = $model->displayField;

    $breadcrumbPrimaryKey = $model->primaryKey;

    return compact('parentId', 'breadcrumbs', 'breadcrumbDisplayField', 'breadcrumbPrimaryKey');

  }

  function hiddenFields(&$model, $paramFields = array()) {

    if (!isset($model->hideableFields)
    || !is_array($model->hideableFields)
    || empty($model->hideableFields)) {
      return false;
    }

    $fieldsWithData = array();
    if ($model->id && is_array($model->data[$model->alias])) {
      $fieldsWithData = array_keys($model->data[$model->alias]);
    }
    $fieldsWithData += $paramFields;

    if (empty($fieldsWithData)) {
      return false;
    }

    $hiddenFields = array_intersect($model->hideableFields, $fieldsWithData);

    if (empty($hiddenFields)) {
      return false;
    }

    return $hiddenFields;

  }

  function getInfo(&$model, $key = null) {

    if ($key
    && isset($this->info[$model->alias][$key])) {
      return $this->info[$model->alias][$key];
    } elseif (is_null($key)
    && isset($this->info[$model->alias])) {
      return $this->info[$model->alias];
    }

    $modelClass = $model->alias;
    $primaryKey = $model->primaryKey;
    $displayField = $model->displayField;

    $schema = $model->schema();

    $controllerName = Inflector::pluralize($modelClass);
    $controllerPath = low(Inflector::underscore($controllerName));

    $singularVar = Inflector::variable($modelClass);
    $pluralVar = Inflector::variable($controllerName);

    $singularHumanName = Inflector::humanize(Inflector::singularize($controllerPath));
    $pluralHumanName = Inflector::humanize($controllerPath);

    $isWith = $model->isWith;

    $treeBehavior = $this->settings[$model->alias]['tree_behavior_name'];

    $isTree = $model->Behaviors->attached($treeBehavior);

    $summaryField = $orderField = null;
    if ($isAutoFillSummary = $model->Behaviors->attached('AutoFillSummary')) {
      $summaryField = $model->Behaviors->AutoFillSummary->settings[$modelClass]['summary_field'];
    }

    if ($isOrderable = $model->Behaviors->attached('Sequence')) {
      $orderField = $model->Behaviors->Sequence->settings[$modelClass]['order_field'];
    }

    $belongsTo = $model->belongsTo;

    foreach ($belongsTo as $alias => $assocData) {

      if ($modelClass != $assocData['className']) {
        $belongsToIsTree = $model->$alias->Behaviors->attached($treeBehavior);
      } else {
        $belongsToIsTree = $isTree;
      }

      $belongsTo[$alias] += array(
        'primaryKey' => $model->{$alias}->primaryKey,
        'displayField' => $model->{$alias}->displayField,
        'schema' =>  $model->{$alias}->schema(),
        'controllerPath' => strtolower(Inflector::underscore(Inflector::pluralize($assocData['className']))),
        'pluralVar' => Inflector::variable(Inflector::pluralize($alias)),
        'isTree' => $belongsToIsTree,
        'singularHumanName' => Inflector::humanize(Inflector::singularize($assocData['className'])),
      );
      if ($isWith) {
        $belongsToKeys = array_keys($belongsTo);
        $otherBelongsToAlias = current(array_diff($belongsToKeys, array($alias)));
        foreach ($model->$alias->hasAndBelongsToMany as $belongsToHasAndBelongsToManyAlias => $belongsToHasAndBelongsToManyAssocData) {
          if ($belongsToHasAndBelongsToManyAssocData['className'] == $belongsTo[$otherBelongsToAlias]['className']
          && $belongsToHasAndBelongsToManyAssocData['joinTable'] == $controllerPath
          && isset($belongsToHasAndBelongsToManyAssocData['form'])
          && $belongsToHasAndBelongsToManyAssocData['form'] == 'ordered') {
            $schema[$assocData['foreignKey']]['hide_on_index'] = true;
          }
        }
      }
    }

    $hasOne = $model->hasOne;

    foreach ($hasOne as $alias => $assocData) {

      $hasOne[$alias] += array(
        'primaryKey' => $model->{$alias}->primaryKey,
        'displayField' => $model->{$alias}->displayField,
        'schema' =>  $model->{$alias}->schema(),
        'controllerPath' => strtolower(Inflector::underscore(Inflector::pluralize($assocData['className']))),
        'singularHumanName' => Inflector::humanize((Inflector::underscore($assocData['className']))),
      );

    }

    $hasMany = $model->hasMany;

    foreach ($hasMany as $alias => $assocData) {

      $hasMany[$alias] += array(
        'primaryKey' => $model->{$alias}->primaryKey,
        'displayField' => $model->{$alias}->displayField,
        'schema' =>  $model->{$alias}->schema(),
        'controllerPath' => low(Inflector::underscore(Inflector::pluralize($assocData['className']))),
      );

    }

    $hasAndBelongsToMany = $model->hasAndBelongsToMany;

    foreach ($hasAndBelongsToMany as $alias => $assocData) {

      if ($modelClass != $assocData['className']) {
        $hasAndBelongsToManyIsTree = $model->$alias->Behaviors->attached($treeBehavior);
      } else {
        $hasAndBelongsToManyIsTree = $model->Behaviors->attached($treeBehavior);
      }

      $hasAndBelongsToMany[$alias] += array(
        'pluralVar' => Inflector::variable(Inflector::pluralize($alias)),
        'isTree' => $hasAndBelongsToManyIsTree,
        'withModel' => Inflector::classify($assocData['joinTable']),
        'withControllerPath' => low(Inflector::underscore(Inflector::pluralize($assocData['joinTable']))),
        'primaryKey' => $model->{$alias}->primaryKey,
        'displayField' => $model->{$alias}->displayField,
        'schema' =>  $model->{$alias}->schema(),
        'controllerPath' => low(Inflector::underscore(Inflector::pluralize($assocData['className']))),
      );

    }

    $associations = array(
      'belongsTo' => $belongsTo,
      'hasOne' => $hasOne,
      'hasMany' => $hasMany,
      'hasAndBelongsToMany' => $hasAndBelongsToMany,
    );

    $this->info[$model->alias] = compact(
      'modelClass',
      'primaryKey',
      'displayField',
      'schema',
      'associations',
      'controllerName',
      'controllerPath',
      'singularVar',
      'pluralVar',
      'singularHumanName',
      'pluralHumanName',
      'isWith',
      'isTree',
      'isOrderable',
      'isAutoFillSummary',
      'orderField',
      'summaryField'
    );

    if ($key
    && isset($this->info[$model->alias][$key])) {
      return $this->info[$model->alias][$key];
    }

    return $this->info[$model->alias];

  }

}
?>
