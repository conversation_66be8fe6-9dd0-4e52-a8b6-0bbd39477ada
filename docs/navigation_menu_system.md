# Navigation Menu System

This document describes the new configurable navigation menu system that allows CMS management of navigation items, particularly the 'Far & Wide' section.

## Overview

The navigation system has been enhanced to support database-driven menu configuration while maintaining backward compatibility. The 'Far & Wide' navigation section can now be managed through the CMS instead of being hardcoded.

## Database Structure

### navigation_menus Table

The `navigation_menus` table stores hierarchical menu items with the following fields:

- `id` - Primary key
- `parent_id` - Parent menu item (for hierarchical structure)
- `lft`, `rght` - Tree structure fields for efficient hierarchical queries
- `child_count`, `direct_child_count` - Cached child counts
- `name` - Display name of the menu item
- `url` - URL path for the menu item
- `menu_type` - Type of menu (far_wide, main_nav, footer)
- `order` - Display order within the same level
- `published` - Whether the item is published (1) or hidden (0)
- `created`, `modified` - Timestamps

## Installation

**Note**: The `navigation_menus` table already exists in the database schema.

1. Run the migration to populate the table with Far & Wide menu data:
   ```bash
   cd app/config/sql
   php run_migration.php
   ```

2. The migration will populate the `navigation_menus` table with the Far & Wide menu items:
   - South America (/landing_pages/south_america)
   - Worldwide (/landing_pages/worldwide)
     - Cruises (/landing_pages/cruises)
     - Escorted Tours (/landing_pages/escorted_tours)

## CMS Management

### Accessing the Navigation Menu Manager

1. Log into the CMS admin interface
2. Navigate to `/webadmin/navigation_menus`
3. Use the menu type filter to view different menu types (default: Far & Wide)

### Managing Menu Items

#### Adding New Items
1. Click "Add" in the navigation menus interface
2. Fill in the required fields:
   - **Name**: Display text for the menu item
   - **URL**: Full URL path (e.g., `/landing_pages/new_page`)
   - **Menu Type**: Select the menu type (Far & Wide, Main Navigation, Footer)
   - **Parent Item**: Select a parent to create a sub-menu item (optional)
   - **Order**: Display order (optional, lower numbers appear first)
   - **Published**: Check to make the item visible

#### Editing Items
1. Click "Edit" next to any menu item
2. Modify the fields as needed
3. Click "Save" or "Save and Go Back"

#### Reordering Items
- Use the up (↑) and down (↓) arrows in the reorder column
- Items are displayed in hierarchical order based on the tree structure

#### Deleting Items
- Click "Delete" next to any menu item
- Confirm the deletion when prompted
- **Note**: Deleting a parent item will also delete all its children

## Technical Implementation

### Navigation Component

The `NavigationComponent` has been enhanced with:
- `getFarWideMenuItems()` method to retrieve menu items from database
- Caching support for improved performance
- Fallback to hardcoded values for backward compatibility

### Templates Updated

The following templates now use dynamic menu data:
- `app/controllers/navigation_controller.php` - API endpoints
- `app/views/elements/chrome/mega_menu_api.ctp` - Desktop megamenu
- `app/views/elements/chrome/page_header.ctp` - Main site navigation
- Mobile menu templates automatically inherit the changes

### Caching

Menu items are cached using the key `navigation_menu_far_wide` in the `navigation` cache configuration. The cache is automatically cleared when menu items are added, edited, or deleted.

## Menu Types

The system supports multiple menu types:
- **far_wide**: Far & Wide navigation section
- **main_nav**: Main navigation (for future expansion)
- **footer**: Footer navigation (for future expansion)

## Hierarchical Structure

The system supports unlimited levels of nested menu items:
- Parent items can have multiple children
- Children can have their own children (sub-sub-menus)
- The tree structure is maintained using the nested set model

## Backward Compatibility

If no database menu items exist for a menu type, the system falls back to hardcoded values to ensure the site continues to function normally.

## API Endpoints

The navigation API endpoints (`/megamenu` and `/mmenu`) automatically serve the dynamic menu data, so the WordPress blog will automatically receive updated navigation without any changes needed.

## Troubleshooting

### Menu Items Not Appearing
1. Check that items are marked as "Published"
2. Verify the menu_type is set correctly
3. Clear the navigation cache if needed

### Cache Issues
The navigation cache is automatically cleared when menu items are modified, but you can manually clear it if needed:
```php
Cache::delete('navigation_menu_far_wide', 'navigation');
```

### Database Issues
If the migration fails, you can manually run the SQL from:
`app/config/sql/migrations/add_navigation_menus_table.sql`
